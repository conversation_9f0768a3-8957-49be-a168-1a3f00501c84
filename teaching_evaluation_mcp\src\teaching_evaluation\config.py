"""课堂教学分析配置模块"""
import os
from pydantic import BaseModel, Field, field_validator
from dotenv import load_dotenv

# 在应用启动时加载.env文件中的环境变量
load_dotenv()


class TeachingEvaluationConfig(BaseModel):
    """
    应用配置类，集中管理所有可配置项。
    配置值优先从环境变量中读取，若环境变量未设置，则使用代码中定义的默认值。
    """

    # --- AI服务配置 ---
    ai_api_key: str = Field(description="AI服务API密钥")
    ai_provider: str = Field(
        default="dashscope",
        description="AI服务提供商（固定为dashscope）"
    )
    ai_model_name: str = Field(
        default="deepseek-v3",
        description="AI模型名称"
    )
    ai_timeout: int = Field(
        default=120,
        description="AI服务请求超时时间（秒）"
    )
    ai_max_retries: int = Field(
        default=3,
        description="AI服务请求最大重试次数"
    )

    # --- 分析参数配置 ---
    max_tokens: int = Field(default=8000, description="最大token数量")
    temperature: float = Field(default=0.7, description="生成温度")

    # --- 输入限制配置 ---
    max_transcript_length: int = Field(
        default=50000,
        description="转录文本最大长度（字符数）"
    )

    # --- 输出路径配置 ---
    output_dir: str = Field(default="outputs", description="生成的分析报告文件存放的根目录")

    # --- 日志系统配置 ---
    log_dir: str = Field(default="logs", description="日志文件的存放目录")
    log_level: str = Field(default="INFO", description="日志记录的最低级别")
    log_console: bool = Field(default=True, description="是否同时将日志输出到控制台")

    # --- 调试配置 ---
    save_raw_responses: bool = Field(default=False, description="是否保存AI原始响应到文件")

    @field_validator('ai_timeout')
    @classmethod
    def validate_timeout(cls, v):
        if v <= 0:
            raise ValueError('超时时间必须大于0')
        return v

    @field_validator('temperature')
    @classmethod
    def validate_temperature(cls, v):
        if not 0 <= v <= 2:
            raise ValueError('温度值必须在0-2之间')
        return v
    
    @classmethod
    def from_env(cls) -> "TeachingEvaluationConfig":
        """
        从环境变量构造配置实例。

        该方法会逐一检查每个配置项对应的环境变量，如果存在则使用环境变量的值，
        否则使用在类中定义的默认值。对于API Key这类敏感信息，强制要求
        必须通过环境变量设置。

        Raises:
            ValueError: 如果未设置必要的环境变量（如AI_API_KEY）。

        Returns:
            TeachingEvaluationConfig: 一个包含所有最终配置值的实例。
        """
        ai_api_key = os.getenv("DASHSCOPE_API_KEY") or os.getenv("AI_API_KEY")
        if not ai_api_key:
            raise ValueError("请设置环境变量 DASHSCOPE_API_KEY 或 AI_API_KEY")

        # 获取项目根目录（从当前文件位置向上两级）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))

        # 设置默认路径
        default_output_dir = os.path.join(project_root, "outputs")
        default_log_dir = os.path.join(project_root, "logs")

        return cls(
            ai_api_key=ai_api_key,
            ai_provider=os.getenv("AI_PROVIDER", "dashscope"),
            ai_model_name=os.getenv("AI_MODEL_NAME", "deepseek-v3"),
            ai_timeout=int(os.getenv("AI_TIMEOUT", "120")),
            ai_max_retries=int(os.getenv("AI_MAX_RETRIES", "3")),
            max_tokens=int(os.getenv("MAX_TOKENS", "8000")),
            temperature=float(os.getenv("TEMPERATURE", "0.7")),
            max_transcript_length=int(os.getenv("MAX_TRANSCRIPT_LENGTH", "50000")),
            output_dir=os.getenv("OUTPUT_DIR", default_output_dir),
            log_dir=os.getenv("LOG_DIR", default_log_dir),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_console=os.getenv("LOG_CONSOLE", "true").lower() == "true",
            save_raw_responses=os.getenv("SAVE_RAW_RESPONSES", "false").lower() == "true"
        )

