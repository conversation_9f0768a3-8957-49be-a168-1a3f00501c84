[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "teaching-evaluation-mcp"
version = "1.0.0"
description = "Professional classroom teaching analysis MCP server"
authors = [
    {name = "Teaching Evaluation MCP Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
keywords = ["mcp", "teaching", "evaluation", "ai", "analysis"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Education",
    "Topic :: Scientific/Engineering :: Artificial Intelligence"
]

dependencies = [
    # MCP 框架
    "fastmcp>=2.0.0",
    
    # 数据处理和验证
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    
    # HTTP 客户端
    "httpx>=0.25.0",
    
    # 雷达图生成
    "plotly>=5.15.0",
    "kaleido>=0.2.1",
    
    # 日志和工具
    "rich>=13.0.0",
    "typer>=0.9.0"
]

[project.optional-dependencies]
dev = [
    # 测试框架
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    
    # 代码质量
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.5.0",
    
    # 文档生成
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.0.0"
]

test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0"
]

docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.0.0"
]

[project.urls]
Homepage = "https://github.com/teaching-evaluation-mcp/teaching-evaluation-mcp"
Documentation = "https://teaching-evaluation-mcp.readthedocs.io"
Repository = "https://github.com/teaching-evaluation-mcp/teaching-evaluation-mcp"
Issues = "https://github.com/teaching-evaluation-mcp/teaching-evaluation-mcp/issues"

[project.scripts]
teaching-evaluation-mcp = "teaching_evaluation.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
teaching_evaluation = ["templates/*.txt", "config/*.yaml"]

# Black 代码格式化配置
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除的目录
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

# Ruff 代码检查配置
[tool.ruff]
target-version = "py38"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]

# MyPy 类型检查配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "fastmcp.*",
    "plotly.*",
    "kaleido.*"
]
ignore_missing_imports = true

# Pytest 配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests"
]

# Coverage 配置
[tool.coverage.run]
source = ["src"]
branch = true
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__init__.py"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"
