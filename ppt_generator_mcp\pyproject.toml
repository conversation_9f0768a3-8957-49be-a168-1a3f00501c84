[project]
name = "ppt-generator-mcp"
version = "0.1.0"
description = "MCP server for AI PPT generation using OpenAI/DeepSeek API"
readme = "README.md"
requires-python = ">=3.10, <3.13"
dependencies = [
    "mcp[cli]>=1.2.0",
    "pydantic>=2.0.0",
    "requests>=2.31.0",
    "openai>=1.0.0",
    "python-dotenv>=1.0.0",
    "httpx>=0.24.0",
    "langgraph>=0.1.0",
    "fastapi>=0.104.0",
    "fastmcp>=2.0.0",
]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]