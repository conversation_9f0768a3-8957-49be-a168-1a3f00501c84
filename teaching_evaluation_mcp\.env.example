# Teaching Evaluation MCP Server 环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# =============================================================================
# AI 服务配置 (DashScope)
# =============================================================================

# DashScope API Key (必填)
# 从阿里云 DashScope 控制台获取: https://dashscope.console.aliyun.com/
DASHSCOPE_API_KEY=api_key

# DashScope API 基础URL (可选，默认使用官方地址)
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# 使用的模型名称 (可选，默认使用 DeepSeek-V3)
DASHSCOPE_MODEL_NAME=deepseek-v3

# =============================================================================
# 应用配置
# =============================================================================

# 应用运行环境 (development/production)
# development: 详细日志、调试功能、性能监控
# production: 精简日志、严格验证、优化性能
ENVIRONMENT=development

# 日志级别 (DEBUG/INFO/WARNING/ERROR)
# development建议DEBUG，production建议INFO
LOG_LEVEL=INFO

# 日志文件保存目录 (相对于项目根目录)
LOG_DIR=logs

# 分析报告输出目录 (相对于项目根目录)
OUTPUT_DIR=outputs

# =============================================================================
# AI 模型参数配置
# =============================================================================

# 最大生成Token数 (默认: 8000，基于设计文档)
MAX_TOKENS=8000

# 温度参数，控制生成文本的随机性 (0.0-2.0，默认: 0.7)
TEMPERATURE=0.7

# Top-p 参数，控制生成文本的多样性 (0.0-1.0，默认: 0.9)
TOP_P=0.9

# =============================================================================
# MCP 服务器配置
# =============================================================================

# MCP 服务器名称
MCP_SERVER_NAME=teaching-evaluation-mcp

# MCP 服务器版本
MCP_SERVER_VERSION=1.0.0

# MCP 服务器描述
MCP_SERVER_DESCRIPTION=Professional classroom teaching analysis MCP server

# =============================================================================
# 雷达图生成配置
# =============================================================================

# 雷达图输出格式 (html,png 或 both，默认: both)
CHART_OUTPUT_FORMAT=both

# 雷达图图片质量 (1-4，默认: 2)
CHART_IMAGE_SCALE=2

# 雷达图宽度 (像素，默认: 700)
CHART_WIDTH=700

# 雷达图高度 (像素，默认: 600)
CHART_HEIGHT=600

# =============================================================================
# 输入验证和安全配置
# =============================================================================

# 单次分析的最大文本长度 (字符数，默认: 50000，基于设计文档)
MAX_TRANSCRIPT_LENGTH=50000

# 是否启用输入内容安全过滤 (true/false，默认: true)
# 过滤XSS脚本、JavaScript协议、Base64数据URI等恶意内容
ENABLE_CONTENT_FILTER=true

# =============================================================================
# 性能和网络配置
# =============================================================================

# API 请求超时时间 (秒，默认: 120，基于设计文档)
AI_TIMEOUT=120

# API 请求重试次数 (默认: 3，基于设计文档)
AI_MAX_RETRIES=3

# 重试间隔时间 (秒，默认: 1)
RETRY_DELAY=1

# =============================================================================
# 开发和调试配置 (基于ENVIRONMENT自动调整)
# =============================================================================

# 是否启用详细日志 (true/false)
# 详细日志包括：中间处理步骤、AI请求详情、性能指标等
# development环境建议true，production环境建议false
VERBOSE_LOGGING=true

# 是否保存原始AI响应 (true/false)
# 用途：调试AI响应质量、分析提示词效果、问题排查
# 注意：会增加存储空间使用，生产环境建议关闭
SAVE_RAW_RESPONSES=true

# 是否启用性能监控 (true/false)
# 监控指标：API调用时间、内存使用、成功率、Token消耗等
# development环境建议true，production环境根据需要开启
ENABLE_PERFORMANCE_MONITORING=true

# =============================================================================
# 环境配置说明和注意事项
# =============================================================================

# 当前配置：DEVELOPMENT 模式
# - 日志级别：DEBUG（详细输出）
# - 详细日志：启用（包含中间处理步骤）
# - 原始响应保存：启用（用于调试）
# - 性能监控：启用（记录详细指标）

# 切换到 PRODUCTION 模式时，建议调整：
# ENVIRONMENT=production
# LOG_LEVEL=INFO
# VERBOSE_LOGGING=false
# SAVE_RAW_RESPONSES=false
# ENABLE_PERFORMANCE_MONITORING=false

# 重要提醒：
# 1. 请确保 DASHSCOPE_API_KEY 已正确设置，这是必需的配置项
# 2. 雷达图生成需要安装 kaleido 依赖，如果不需要 PNG 格式可以设置 CHART_OUTPUT_FORMAT=html
# 3. 输入内容过滤基于SecurityValidator类实现，包含XSS、JavaScript等恶意内容检测
# 4. 性能监控基于PerformanceMonitor类实现，记录详细的执行指标
