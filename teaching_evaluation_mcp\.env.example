# DashScope API配置（必需）
DASHSCOPE_API_KEY=your_dashscope_api_key_here
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/api/v1
DASHSCOPE_MODEL_NAME=deepseek-v3

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_CONSOLE=true

# 输出配置
OUTPUT_DIR=outputs

# AI 模型参数配置
MAX_TOKENS=8000
TEMPERATURE=0.7
TOP_P=0.9

# 输入验证和安全配置
MAX_TRANSCRIPT_LENGTH=50000

# 性能和网络配置
AI_TIMEOUT=120
AI_MAX_RETRIES=3
RETRY_DELAY=1

# 配置说明：
# DASHSCOPE_API_KEY: DashScope AI平台的API密钥，必须设置
# DASHSCOPE_BASE_URL: DashScope API的基础URL，一般使用默认值
# DASHSCOPE_MODEL_NAME: 使用的AI模型名称
# MAX_TOKENS: 最大生成Token数，控制AI响应长度
# TEMPERATURE: 温度参数，控制生成文本的随机性 (0.0-2.0)
# TOP_P: Top-p参数，控制生成文本的多样性 (0.0-1.0)
# MAX_TRANSCRIPT_LENGTH: 单次分析的最大文本长度 (字符数)
# AI_TIMEOUT: API请求超时时间 (秒)
# AI_MAX_RETRIES: API请求重试次数
# RETRY_DELAY: 重试间隔时间 (秒)
# LOG_LEVEL: 日志级别 (DEBUG/INFO/WARNING/ERROR)
# LOG_DIR: 日志文件存储目录，相对于项目根目录
# LOG_CONSOLE: 是否同时在控制台输出日志
# OUTPUT_DIR: 分析报告输出目录，相对于项目根目录
