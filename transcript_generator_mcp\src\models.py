"""教学逐字稿生成器数据模型

定义所有用于教学逐字稿生成的Pydantic数据模型，包括输入输出模型、
内部数据模型和状态管理模型。
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Literal
from pydantic import BaseModel, Field, field_validator, ConfigDict
import time


class TranscriptRequest(BaseModel):
    """逐字稿生成请求模型"""
    course_basic_info: str = Field(
        ..., 
        description="课程基本信息（如：年级、科目、课题）",
        min_length=5,
        max_length=200
    )
    teaching_info: str = Field(
        ..., 
        description="教学信息（如：教学目标、重点难点）",
        min_length=10,
        max_length=500
    )
    personal_requirements: Optional[str] = Field(
        None, 
        description="个性化要求（如：特殊教学风格、注意事项）",
        max_length=300
    )
    
    @field_validator('course_basic_info')
    @classmethod
    def validate_course_basic_info(cls, v):
        """验证课程基本信息"""
        if not v.strip():
            raise ValueError('课程基本信息不能为空')
        return v.strip()

    @field_validator('teaching_info')
    @classmethod
    def validate_teaching_info(cls, v):
        """验证教学信息"""
        if not v.strip():
            raise ValueError('教学信息不能为空')
        return v.strip()
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "course_basic_info": "小学三年级数学，第五单元：面积",
                "teaching_info": "让学生理解面积概念，学会计算长方形和正方形的面积",
                "personal_requirements": "注重动手操作，多用生活实例，增加师生互动"
            }
        }
    )


class TeachingProcessInfo(BaseModel):
    """教学环节详细信息模型（用于metadata）"""
    section_id: str = Field(..., description="环节ID（如：process_01）")
    title: str = Field(..., description="环节标题（如：一、课程导入（5分钟））")
    content: str = Field(..., description="环节内容要点")
    duration_minutes: Optional[int] = Field(None, description="预计时长（分钟）")
    transcript_length: Optional[int] = Field(None, description="逐字稿字数")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "section_id": "process_01",
                "title": "一、课程导入（5分钟）",
                "content": "通过生活实例引入面积概念，激发学生学习兴趣",
                "duration_minutes": 5,
                "transcript_length": 856
            }
        }
    )


class TeachingProcess(BaseModel):
    """教学流程大纲模型"""
    total_sections: int = Field(..., description="总环节数")
    total_duration: int = Field(..., description="总预计时长（分钟）")
    flow_content: str = Field(..., description="完整的教学流程内容")
    sections_summary: List[str] = Field(..., description="各环节标题列表")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total_sections": 5,
                "total_duration": 40,
                "flow_content": "# 一、课程导入（5分钟）\n...",
                "sections_summary": [
                    "一、课程导入（5分钟）",
                    "二、新知探索（15分钟）",
                    "三、实践练习（10分钟）",
                    "四、巩固提升（8分钟）",
                    "五、课程总结（2分钟）"
                ]
            }
        }
    )


class TeachingSection(BaseModel):
    """教学环节模型"""
    section_id: str = Field(..., description="环节ID（如：process_01）")
    title: str = Field(..., description="环节标题（如：一、课程导入（5分钟））")
    content: str = Field(..., description="环节内容要点")
    transcript: str = Field(..., description="详细逐字稿内容")
    duration_minutes: Optional[int] = Field(None, description="预计时长（分钟）")
    
    @field_validator('section_id')
    @classmethod
    def validate_section_id(cls, v):
        """验证环节ID格式"""
        if not v.startswith('process_'):
            raise ValueError('环节ID必须以process_开头')
        return v

    @field_validator('duration_minutes')
    @classmethod
    def validate_duration(cls, v):
        """验证时长"""
        if v is not None and (v < 1 or v > 60):
            raise ValueError('环节时长必须在1-60分钟之间')
        return v


class TranscriptResponse(BaseModel):
    """逐字稿生成响应模型"""
    final_transcript: str = Field(..., description="完整的教学逐字稿")
    teaching_sections: List[TeachingSection] = Field(..., description="教学环节列表")
    total_sections: int = Field(..., description="总环节数")
    estimated_duration: int = Field(..., description="预计总时长（分钟）")
    generation_time: float = Field(..., description="生成耗时（秒）")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    output_directory: Optional[str] = Field(None, description="输出文件目录路径")
    
    @field_validator('total_sections')
    @classmethod
    def validate_total_sections(cls, v, info):
        """验证总环节数与实际环节数一致"""
        if info.data and 'teaching_sections' in info.data:
            actual_count = len(info.data['teaching_sections'])
            if v != actual_count:
                raise ValueError(f'总环节数({v})与实际环节数({actual_count})不一致')
        return v


class ProgressUpdate(BaseModel):
    """进度更新消息模型"""
    status: Literal[
        "started",                      # 任务开始
        "flow_generation_started",      # 开始生成教学流程
        "flow_generation_completed",    # 教学流程生成完成
        "parallel_generation_started",  # 开始并行生成逐字稿
        "section_generation_started",   # 开始生成单个环节
        "section_streaming",            # 环节内容流式输出
        "section_generation_completed", # 单个环节生成完成
        "parallel_generation_completed", # 并行生成完成
        "merge_started",                # 开始合并逐字稿
        "merge_completed",              # 合并完成
        "saving_files",                 # 保存文件中
        "completed",                    # 任务完成
        "model_usage"                   # 模型用量报告
    ] = Field(..., description="状态类型")
    message: str = Field(..., description="状态描述信息")
    progress: Optional[float] = Field(None, description="进度百分比（0-100）")
    section_id: Optional[str] = Field(None, description="当前处理的环节ID")
    chunk: Optional[str] = Field(None, description="流式内容片段")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    
    @field_validator('progress')
    @classmethod
    def validate_progress(cls, v):
        """验证进度百分比"""
        if v is not None and (v < 0 or v > 100):
            raise ValueError('进度百分比必须在0-100之间')
        return v


class ModelUsageInfo(BaseModel):
    """模型用量信息"""
    provider: str = Field(..., description="模型提供商")
    model_name: str = Field(..., description="模型名称")
    input_tokens: int = Field(..., description="输入Token数")
    output_tokens: int = Field(..., description="输出Token数")
    total_tokens: int = Field(..., description="总Token数")
    cost_estimate: Optional[float] = Field(None, description="预估成本（元）")
    response_time: float = Field(..., description="响应时间（秒）")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    
    @property
    def efficiency_score(self) -> float:
        """计算效率分数（tokens/second）"""
        return self.total_tokens / max(self.response_time, 0.001)
    
    @field_validator('input_tokens', 'output_tokens', 'total_tokens')
    @classmethod
    def validate_tokens(cls, v):
        """验证Token数量"""
        if v < 0:
            raise ValueError('Token数量不能为负数')
        return v

    @field_validator('response_time')
    @classmethod
    def validate_response_time(cls, v):
        """验证响应时间"""
        if v < 0:
            raise ValueError('响应时间不能为负数')
        return v


class ErrorInfo(BaseModel):
    """错误信息模型"""
    error_type: str = Field(..., description="错误类型")
    error_message: str = Field(..., description="错误描述")
    error_details: Optional[str] = Field(None, description="详细错误信息")
    stage: Optional[str] = Field(None, description="发生错误的阶段")
    section_id: Optional[str] = Field(None, description="相关环节ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    retry_suggested: bool = Field(False, description="是否建议重试")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "error_type": "api_request_failed",
                "error_message": "AI服务调用失败",
                "error_details": "HTTP 429: Rate limit exceeded",
                "stage": "section_generation",
                "section_id": "process_01",
                "retry_suggested": True
            }
        }
    )


class TeachingFlow(BaseModel):
    """教学流程模型"""
    content: str = Field(..., description="完整的教学流程内容")
    sections_count: int = Field(..., description="环节数量")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        """验证教学流程内容"""
        if not v.strip():
            raise ValueError('教学流程内容不能为空')
        if len(v) < 100:
            raise ValueError('教学流程内容过短，至少需要100个字符')
        return v.strip()


class SectionParseResult(BaseModel):
    """环节解析结果模型"""
    section_id: str = Field(..., description="环节ID")
    title: str = Field(..., description="环节标题")
    content: str = Field(..., description="环节内容")
    duration_minutes: Optional[int] = Field(None, description="预计时长（分钟）")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "section_id": "process_01",
                "title": "一、课程导入（5分钟）",
                "content": "通过生活实例引入面积概念，激发学生学习兴趣"
            }
        }
    )


class AIClientConfig(BaseModel):
    """AI客户端配置模型"""
    provider: str = Field(..., description="AI服务提供商")
    api_key: str = Field(..., description="API密钥")
    model_name: str = Field(..., description="模型名称")
    base_url: Optional[str] = Field(None, description="API基础URL")
    timeout: int = Field(default=60, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    
    @field_validator('provider')
    @classmethod
    def validate_provider(cls, v):
        """验证AI提供商"""
        allowed_providers = ['zhipuai', 'siliconflow', 'dashscope', 'openai']
        if v not in allowed_providers:
            raise ValueError(f'AI提供商必须是以下之一: {allowed_providers}')
        return v


class ServiceMetrics(BaseModel):
    """服务指标模型"""
    total_requests: int = Field(default=0, description="总请求数")
    successful_requests: int = Field(default=0, description="成功请求数")
    failed_requests: int = Field(default=0, description="失败请求数")
    average_response_time: float = Field(default=0.0, description="平均响应时间（秒）")
    total_tokens_used: int = Field(default=0, description="总Token使用量")
    total_cost: float = Field(default=0.0, description="总成本（元）")
    uptime_seconds: float = Field(default=0.0, description="运行时间（秒）")
    last_updated: datetime = Field(default_factory=datetime.now, description="最后更新时间")
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
    
    @property
    def failure_rate(self) -> float:
        """计算失败率"""
        return 100.0 - self.success_rate


# 类型别名
ProgressCallback = Optional[callable]
ErrorCallback = Optional[callable]
