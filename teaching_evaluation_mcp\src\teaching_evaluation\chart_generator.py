"""雷达图生成模块

基于Plotly生成专业的教学评价雷达图，支持HTML和PNG两种输出格式。
"""

import json
import re
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Tuple, Optional, List

import plotly.graph_objects as go
import plotly.io as pio

from .logger import get_logger
from .models import FiveDimensionScores


class RadarChartGeneratorError(Exception):
    """雷达图生成异常类"""
    pass


class RadarChartGenerator:
    """基于Plotly的教学评价雷达图生成器
    
    功能：
    - 基于五维度评分数据生成专业雷达图
    - 支持HTML和PNG两种输出格式
    - 提供图表样式配置和优化
    - 数据验证和标准化处理
    """

    def __init__(self, output_dir: str = "outputs"):
        """初始化雷达图生成器
        
        Args:
            output_dir: 输出目录路径
        """
        self.logger = get_logger(__name__)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 五个维度的标准名称（按教学评价逻辑排序）
        self.dimensions = [
            "学生学习",
            "教师教学", 
            "课程性质",
            "课堂文化",
            "社会情感"
        ]
        
        # 维度的英文字段名映射
        self.dimension_field_mapping = {
            "学生学习": "student_learning",
            "教师教学": "teacher_teaching",
            "课程性质": "curriculum_nature", 
            "课堂文化": "classroom_culture",
            "社会情感": "social_emotion"
        }
        
        # 设置Plotly的默认配置
        try:
            pio.kaleido.scope.mathjax = None  # 避免MathJax相关问题
        except Exception as e:
            self.logger.warning(f"Plotly配置设置失败: {e}")
        
        self.logger.info("雷达图生成器初始化完成")

    def generate_radar_chart(
        self,
        scores: FiveDimensionScores,
        title: str = "课堂教学评价雷达图",
        timestamp: Optional[str] = None,
        output_dir: Optional[str] = None
    ) -> Tuple[str, Optional[str]]:
        """生成专业的教学评价雷达图

        Args:
            scores: 五维度评分数据
            title: 图表标题
            timestamp: 时间戳，用于文件命名
            output_dir: 自定义输出目录，如果为None则使用默认目录

        Returns:
            Tuple[str, Optional[str]]: (HTML文件路径, PNG文件路径)

        Raises:
            RadarChartGeneratorError: 雷达图生成失败
        """
        try:
            if timestamp is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 确定输出目录
            target_output_dir = Path(output_dir) if output_dir else self.output_dir

            self.logger.info(f"开始生成雷达图: {title}")
            self.logger.info(f"输出目录: {target_output_dir}")
            
            # 验证和标准化数据
            validated_scores = self._validate_and_normalize_scores(scores)
            
            # 准备数据（确保维度顺序一致）
            categories = []
            values = []
            
            for dimension in self.dimensions:
                field_name = self.dimension_field_mapping[dimension]
                score = getattr(validated_scores, field_name)
                categories.append(dimension)
                values.append(score)
            
            # 闭合多边形（雷达图要求）
            values += values[:1]
            categories += categories[:1]
            
            # 创建雷达图
            fig = go.Figure()
            
            # 添加主要数据轨迹
            fig.add_trace(go.Scatterpolar(
                r=values,
                theta=categories,
                fill='toself',
                name='教学评价',
                line=dict(color='rgb(0, 123, 255)', width=3),
                fillcolor='rgba(0, 123, 255, 0.25)',
                marker=dict(
                    size=8,
                    color='rgb(0, 123, 255)',
                    line=dict(color='white', width=2)
                ),
                hovertemplate='<b>%{theta}</b><br>评分: %{r:.1f}/10<extra></extra>'
            ))
            
            # 设置布局
            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 10],
                        tickmode='linear',
                        tick0=0,
                        dtick=2,
                        tickfont=dict(size=12),
                        gridcolor='rgba(128, 128, 128, 0.3)',
                        gridwidth=1
                    ),
                    angularaxis=dict(
                        tickfont=dict(size=14, color='rgb(67, 67, 67)'),
                        linecolor='rgba(128, 128, 128, 0.5)',
                        gridcolor='rgba(128, 128, 128, 0.3)'
                    ),
                    bgcolor='rgba(255, 255, 255, 0.8)'
                ),
                title=dict(
                    text=title,
                    x=0.5,
                    y=0.95,
                    font=dict(size=18, color='rgb(67, 67, 67)', family='Arial, sans-serif')
                ),
                showlegend=True,
                legend=dict(
                    x=0.02,
                    y=0.98,
                    bgcolor='rgba(255, 255, 255, 0.8)',
                    bordercolor='rgba(128, 128, 128, 0.5)',
                    borderwidth=1
                ),
                width=700,
                height=600,
                margin=dict(l=80, r=80, t=100, b=80),
                paper_bgcolor='white',
                plot_bgcolor='white'
            )
            
            # 保存HTML文件
            html_filename = f"radar_chart_{timestamp}.html"
            html_path = target_output_dir / html_filename
            
            fig.write_html(
                str(html_path),
                include_plotlyjs='cdn',
                config={
                    'displayModeBar': True,
                    'displaylogo': False,
                    'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d']
                }
            )
            
            self.logger.info(f"HTML雷达图已保存: {html_path}")
            
            # 尝试保存PNG文件
            png_path = None
            try:
                png_filename = f"radar_chart_{timestamp}.png"
                png_path = target_output_dir / png_filename
                
                fig.write_image(str(png_path), width=700, height=600, scale=2)
                self.logger.info(f"PNG雷达图已保存: {png_path}")
                
            except Exception as e:
                self.logger.warning(f"PNG保存失败: {e}，请确保已安装kaleido")
                png_path = None
            
            self.logger.info(f"雷达图生成完成: HTML={html_path}, PNG={png_path}")
            
            return str(html_path), str(png_path) if png_path else None
            
        except Exception as e:
            error_msg = f"雷达图生成失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise RadarChartGeneratorError(error_msg) from e

    def _validate_and_normalize_scores(self, scores: FiveDimensionScores) -> FiveDimensionScores:
        """验证和标准化评分数据
        
        Args:
            scores: 原始评分数据
            
        Returns:
            FiveDimensionScores: 验证后的评分数据
            
        Raises:
            RadarChartGeneratorError: 数据验证失败
        """
        try:
            # Pydantic模型已经进行了基本验证（1.0-10.0范围）
            # 这里进行额外的业务逻辑验证
            
            validated_scores = {}
            
            for dimension in self.dimensions:
                field_name = self.dimension_field_mapping[dimension]
                score = getattr(scores, field_name)
                
                # 确保评分在合理范围内
                if not (1.0 <= score <= 10.0):
                    self.logger.warning(f"{dimension}评分超出范围: {score}，将调整到合理范围")
                    score = max(1.0, min(10.0, score))
                
                validated_scores[field_name] = score
            
            # 创建新的验证后的评分对象
            return FiveDimensionScores(**validated_scores)
            
        except Exception as e:
            error_msg = f"评分数据验证失败: {str(e)}"
            self.logger.error(error_msg)
            raise RadarChartGeneratorError(error_msg) from e

    def validate_scores(self, scores: FiveDimensionScores) -> bool:
        """验证评分数据的完整性和有效性
        
        Args:
            scores: 待验证的评分数据
            
        Returns:
            bool: 验证是否通过
        """
        try:
            for dimension in self.dimensions:
                field_name = self.dimension_field_mapping[dimension]
                score = getattr(scores, field_name)
                
                if not isinstance(score, (int, float)):
                    self.logger.error(f"{dimension}评分不是数值类型: {type(score)}")
                    return False
                
                if not (1.0 <= score <= 10.0):
                    self.logger.error(f"{dimension}评分超出范围: {score}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"评分验证过程中发生错误: {e}")
            return False

    def get_chart_statistics(self, scores: FiveDimensionScores) -> Dict[str, float]:
        """获取雷达图的统计信息
        
        Args:
            scores: 评分数据
            
        Returns:
            Dict[str, float]: 统计信息
        """
        try:
            score_values = []
            for dimension in self.dimensions:
                field_name = self.dimension_field_mapping[dimension]
                score = getattr(scores, field_name)
                score_values.append(score)
            
            return {
                "average_score": sum(score_values) / len(score_values),
                "max_score": max(score_values),
                "min_score": min(score_values),
                "score_range": max(score_values) - min(score_values),
                "total_dimensions": len(score_values)
            }
            
        except Exception as e:
            self.logger.error(f"统计信息计算失败: {e}")
            return {}

    def generate_chart_summary(self, scores: FiveDimensionScores) -> str:
        """生成雷达图的文字总结
        
        Args:
            scores: 评分数据
            
        Returns:
            str: 图表总结文本
        """
        try:
            stats = self.get_chart_statistics(scores)
            
            if not stats:
                return "无法生成图表总结"
            
            avg_score = stats["average_score"]
            max_score = stats["max_score"]
            min_score = stats["min_score"]
            
            # 找出最高分和最低分的维度
            max_dimension = ""
            min_dimension = ""
            
            for dimension in self.dimensions:
                field_name = self.dimension_field_mapping[dimension]
                score = getattr(scores, field_name)
                
                if score == max_score:
                    max_dimension = dimension
                if score == min_score:
                    min_dimension = dimension
            
            summary = f"""
雷达图分析总结：
- 平均评分：{avg_score:.1f}/10
- 最高维度：{max_dimension}（{max_score:.1f}分）
- 最低维度：{min_dimension}（{min_score:.1f}分）
- 评分差距：{stats['score_range']:.1f}分
            """.strip()
            
            return summary
            
        except Exception as e:
            self.logger.error(f"图表总结生成失败: {e}")
            return "图表总结生成失败"
