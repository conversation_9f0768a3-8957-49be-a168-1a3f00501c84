"""教学分析服务模块

集成AI客户端、提示词管理器和雷达图生成器，提供完整的教学分析服务接口
"""

import time
import json
import os
import re
from datetime import datetime
from typing import Tuple, Dict, Any, Optional
from fastmcp import Context

from .config import TeachingEvaluationConfig
from .ai_client import DashScopeClient
from .prompt_manager import PromptManager
from .chart_generator import RadarChartGenerator
from .models import ModelUsageInfo, AnalysisReport, FiveDimensionAnalysis, FiveDimensionScores
from .logger import get_logger


class TeachingAnalysisError(Exception):
    """教学分析服务异常类"""
    pass


class TeachingAnalyzer:
    """教学分析服务类
    
    集成AI客户端、提示词管理器和雷达图生成器，提供完整的教学分析服务。
    负责：
    - 统一的AI调用接口
    - 雷达图生成和文件输出
    - 用量统计和报告
    - 错误处理和重试
    - 实时进度通知
    """

    def __init__(self, config: TeachingEvaluationConfig):
        """初始化教学分析服务
        
        Args:
            config: 教学评价配置对象
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 初始化组件
        self.ai_client = DashScopeClient(config)
        self.prompt_manager = PromptManager()
        self.chart_generator = RadarChartGenerator(config.output_dir)
        
        self.logger.info("教学分析服务初始化完成")

    def _create_analysis_directory(self, analysis_id: str) -> str:
        """
        为每一次教学分析任务创建一个唯一的专属目录。
        这可以防止不同任务的产出文件互相覆盖。

        Args:
            analysis_id: 分析任务ID

        Returns:
            str: 创建好的专属目录的绝对路径。
        """
        # 生成安全的文件名（移除Windows/Linux非法字符）
        safe_id = re.sub(r'[<>:"/\\|?*]', '_', analysis_id)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建目录结构: outputs/analysis_时间戳_ID/
        analysis_dir = os.path.join(
            self.config.output_dir,
            f"analysis_{timestamp}_{safe_id}"
        )
        os.makedirs(analysis_dir, exist_ok=True)
        self.logger.info(f"创建分析目录: {analysis_dir}")

        # 创建charts子目录
        charts_dir = os.path.join(analysis_dir, "charts")
        os.makedirs(charts_dir, exist_ok=True)
        self.logger.debug(f"创建图表目录: {charts_dir}")

        return analysis_dir

    async def analyze_teaching_transcript(
        self,
        transcript: str,
        ctx: Optional[Context] = None
    ) -> Tuple[AnalysisReport, ModelUsageInfo]:
        """分析教学转录文本并生成雷达图
        
        执行完整的教学分析流程，包括：
        1. AI模型分析生成文本报告
        2. 提取五维度评分数据
        3. 生成雷达图（HTML和PNG格式）
        4. 实时进度报告
        
        Args:
            transcript: 课堂教学转录文本
            custom_requirements: 用户自定义分析要求
            ctx: MCP上下文对象，用于实时通信
            
        Returns:
            Tuple[AnalysisReport, ModelUsageInfo]: 分析报告和用量信息
            
        Raises:
            TeachingAnalysisError: 教学分析服务调用失败
            ValueError: 输入参数无效
        """
        start_time = time.time()

        async def send_progress(status: str, message: str, extra: Optional[Dict] = None):
            """便捷函数，用于通过日志通道发送结构化的进度更新"""
            if ctx:
                payload = {"status": status, "message": message}
                if extra:
                    payload.update(extra)
                # 将结构化数据作为JSON字符串通过info日志发送
                await ctx.info(json.dumps(payload))
            self.logger.info(f"[进度] {status}: {message}")

        try:
            # 输入验证
            if not transcript or not transcript.strip():
                raise ValueError("转录文本不能为空")
            
            if len(transcript) > self.config.max_transcript_length:
                raise ValueError(f"转录文本长度超过限制（{self.config.max_transcript_length}字符）")
            
            # 报告任务开始
            await send_progress(
                "analysis_started",
                "开始分析教学转录文本",
                {"transcript_length": len(transcript)}
            )

            # 构建提示词
            await send_progress("prompt_building", "正在构建分析提示词...")

            prompt = self.prompt_manager.build_analysis_prompt(
                transcript=transcript
            )

            await send_progress("prompt_completed", "分析提示词构建完成")

            # 调用AI模型
            await send_progress("ai_processing", "AI模型分析中，请稍候...")

            response_text, usage_info = await self.ai_client.generate_text(
                prompt=prompt
            )

            await send_progress("ai_completed", "AI模型分析完成")

            # 报告用量信息
            if ctx:
                await ctx.info(json.dumps({
                    "status": "model_usage",
                    "usage": usage_info.model_dump()
                }))

            # 提取评分数据
            await send_progress("score_extraction", "正在提取五维度评分数据...")
            
            scores = self.prompt_manager.extract_scores_from_response(response_text)

            await send_progress("score_completed", "五维度评分数据提取完成", {"scores": scores})

            # 解析AI响应内容
            await send_progress("parsing_response", "正在解析AI响应内容...")

            overall_evaluation, summary_suggestions, five_dimensions = self._parse_ai_response(response_text)

            await send_progress("parsing_completed", "AI响应内容解析完成")

            # 构建分析报告
            analysis_duration = time.time() - start_time

            # 创建五维评分对象
            five_dimension_scores = FiveDimensionScores(
                student_learning=scores.get("学生学习", 7.0),
                teacher_teaching=scores.get("教师教学", 7.0),
                curriculum_nature=scores.get("课程性质", 7.0),
                classroom_culture=scores.get("课堂文化", 7.0),
                social_emotion=scores.get("社会情感", 7.0)
            )

            # 创建分析报告
            analysis_report = AnalysisReport(
                analysis_id=f"analysis_{int(time.time())}",
                overall_evaluation=overall_evaluation,
                five_dimensions=five_dimensions,
                five_dimension_scores=five_dimension_scores,
                summary_and_suggestions=summary_suggestions,
                word_count=len(response_text),
                analysis_duration=analysis_duration
            )

            # 创建专属目录
            await send_progress("directory_creation", "正在创建分析工作目录...")

            analysis_dir = self._create_analysis_directory(analysis_report.analysis_id)

            await send_progress(
                "directory_created",
                "已创建分析工作目录",
                {"analysis_directory": analysis_dir}
            )

            # 保存分析报告文件
            await send_progress("saving_reports", "正在保存分析报告文件...")

            # 保存Markdown报告
            try:
                self.logger.info("开始保存Markdown报告...")
                markdown_report_path = self._save_markdown_report(
                    analysis_dir, analysis_report, five_dimensions, summary_suggestions, scores
                )
                self.logger.info(f"Markdown报告保存成功: {markdown_report_path}")
            except Exception as e:
                self.logger.error(f"保存Markdown报告失败: {e}")
                markdown_report_path = None

            # 保存元数据
            try:
                self.logger.info("开始保存元数据...")
                metadata_path = self._save_analysis_metadata(
                    analysis_dir, transcript, analysis_report, usage_info
                )
                self.logger.info(f"元数据保存成功: {metadata_path}")
            except Exception as e:
                self.logger.error(f"保存元数据失败: {e}")
                metadata_path = None

            await send_progress("reports_saved", "分析报告文件保存完成", {
                "markdown_report": markdown_report_path,
                "metadata": metadata_path
            })

            # 保存原始AI响应（如果配置启用）
            if self.config.save_raw_responses:
                await send_progress("saving_raw_response", "正在保存原始AI响应...")

                raw_response_path = os.path.join(analysis_dir, f"{analysis_report.analysis_id}_raw_response.txt")
                with open(raw_response_path, 'w', encoding='utf-8') as f:
                    f.write(response_text)
                self.logger.info(f"原始AI响应已保存: {raw_response_path}")

                await send_progress("raw_response_saved", "原始AI响应保存完成")
            else:
                self.logger.info("save_raw_responses配置为False，跳过保存原始响应")
            
            # 生成雷达图
            await send_progress("chart_generation_started", "正在生成五维度雷达图...")

            try:
                # 使用charts子目录
                charts_dir = os.path.join(analysis_dir, "charts")
                chart_title = f"课堂教学评价雷达图 - {analysis_report.analysis_id}"
                html_path, png_path = self.chart_generator.generate_radar_chart(
                    scores=five_dimension_scores,
                    title=chart_title,
                    timestamp=analysis_report.analysis_id.replace("analysis_", ""),
                    output_dir=charts_dir
                )

                await send_progress(
                    "chart_generation_completed",
                    "雷达图生成完成",
                    {
                        "html_path": html_path,
                        "png_path": png_path
                    }
                )

                self.logger.info(f"雷达图生成完成 - HTML: {html_path}, PNG: {png_path}")
                
            except Exception as e:
                self.logger.warning(f"雷达图生成失败: {str(e)}")
                await send_progress(
                    "chart_generation_failed",
                    f"雷达图生成失败: {str(e)}"
                )

            # 报告任务完成
            await send_progress(
                "analysis_completed",
                "课堂教学分析任务完成",
                {
                    "analysis_duration": f"{analysis_duration:.2f}秒",
                    "analysis_id": analysis_report.analysis_id,
                    "analysis_directory": analysis_dir,
                    "scores": scores
                }
            )
            
            self.logger.info(f"教学分析完成 - ID: {analysis_report.analysis_id}, 耗时: {analysis_duration:.2f}秒")
            
            return analysis_report, usage_info
            
        except ValueError as e:
            error_msg = f"输入验证失败: {str(e)}"
            self.logger.error(error_msg)
            if ctx:
                await ctx.error({
                    "type": "input_validation_error",
                    "message": error_msg,
                    "details": str(e)
                })
            raise TeachingAnalysisError(error_msg) from e
            
        except Exception as e:
            error_msg = f"AI服务调用失败: {str(e)}"
            self.logger.error(error_msg)
            if ctx:
                await ctx.error({
                    "type": "ai_service_error",
                    "message": error_msg,
                    "details": str(e)
                })
            raise TeachingAnalysisError(error_msg) from e

    def _parse_ai_response(self, response_text: str) -> Tuple[str, str, FiveDimensionAnalysis]:
        """解析AI响应内容

        Args:
            response_text: AI模型返回的完整响应文本

        Returns:
            Tuple[str, str, FiveDimensionAnalysis]: (整体评价, 总结建议, 五维度分析)
        """

        # 初始化返回值
        overall_evaluation = ""
        summary_suggestions = ""
        five_dimensions = {
            "student_learning": "",
            "teacher_teaching": "",
            "curriculum_nature": "",
            "classroom_culture": "",
            "social_emotion": ""
        }

        # 分段解析AI响应
        sections = self._split_response_into_sections(response_text)

        # 提取整体评价
        overall_evaluation = sections.get("overall", "")

        # 提取总结建议
        summary_suggestions = sections.get("suggestions", "")

        # 调试信息：检查建议内容是否包含不应该的字符
        if "```" in summary_suggestions or "}" in summary_suggestions:
            self.logger.warning(f"建议内容包含不应该的字符: 长度={len(summary_suggestions)}")
            self.logger.warning(f"建议内容预览: {summary_suggestions[:200]}...")

            # 手动清理建议内容，移除JSON代码块
            lines = summary_suggestions.split('\n')
            cleaned_lines = []
            skip_mode = False

            for line in lines:
                line_stripped = line.strip()
                # 如果遇到代码块开始标记，进入跳过模式
                if "```" in line_stripped:
                    skip_mode = not skip_mode
                    continue
                # 如果在跳过模式中，跳过这一行
                if skip_mode:
                    continue
                # 如果遇到单独的大括号，跳过
                if line_stripped in ["{", "}"]:
                    continue
                # 添加有效行
                cleaned_lines.append(line)

            summary_suggestions = '\n'.join(cleaned_lines).strip()
            self.logger.info(f"清理后的建议内容长度: {len(summary_suggestions)}")
            self.logger.info(f"清理后的建议内容预览: {summary_suggestions[:200]}...")

        # 提取五维度分析内容
        self.logger.info("开始提取五维度分析内容")
        five_dimensions = self._extract_five_dimensions(response_text)
        self.logger.info(f"五维度提取结果: {[(k, len(v)) for k, v in five_dimensions.items()]}")

        # 创建五维度分析对象
        five_dimension_analysis = FiveDimensionAnalysis(
            student_learning=five_dimensions.get("student_learning", "学生学习维度分析内容"),
            teacher_teaching=five_dimensions.get("teacher_teaching", "教师教学维度分析内容"),
            curriculum_nature=five_dimensions.get("curriculum_nature", "课程性质维度分析内容"),
            classroom_culture=five_dimensions.get("classroom_culture", "课堂文化维度分析内容"),
            social_emotion=five_dimensions.get("social_emotion", "社会情感维度分析内容")
        )

        return overall_evaluation.strip(), summary_suggestions.strip(), five_dimension_analysis

    def _split_response_into_sections(self, response_text: str) -> Dict[str, str]:
        """将AI响应分割成不同的部分

        Args:
            response_text: AI模型返回的完整响应文本

        Returns:
            Dict[str, str]: 包含不同部分内容的字典
        """
        sections = {
            "overall": "",
            "suggestions": ""
        }

        lines = response_text.strip().split('\n')
        current_section = None
        json_block_encountered = False  # 标志是否已经遇到JSON代码块

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # 识别整体评价部分 - 更精确的匹配
            if any(keyword in line for keyword in ["一、总体评价", "#### 一、总体评价", "一、整体评价"]):
                current_section = "overall"
                self.logger.debug(f"第{i+1}行: 开始收集整体评价 - {line}")
                continue
            # 识别总结与建议部分 - 精确匹配"三、总结与建议"
            elif any(keyword in line for keyword in ["三、总结与建议", "#### 三、总结与建议"]):
                current_section = "suggestions"
                self.logger.debug(f"第{i+1}行: 开始收集总结与建议 - {line}")
                continue
            # 识别其他主要部分的开始，停止当前收集
            elif any(keyword in line for keyword in [
                "二、五维评估", "#### 二、五维评估",
                "四、五维度评分", "#### 四、五维度评分",
                "（一）", "（二）", "（三）", "（四）", "（五）"
            ]):
                # 如果遇到这些标记，停止收集当前部分
                if current_section == "suggestions":
                    self.logger.debug(f"第{i+1}行: 停止收集建议内容 - {line}")
                current_section = None
                continue
            # 特别处理JSON代码块 - 无论在哪个部分都要停止收集，并且永久停止
            elif "```json" in line or "```" in line:
                # 遇到代码块标记，立即停止收集任何内容，并且不再恢复
                if current_section == "suggestions":
                    self.logger.debug(f"第{i+1}行: 遇到代码块，永久停止收集建议内容 - {line}")
                current_section = None
                # 设置标志，表示已经遇到JSON代码块，后续所有内容都不再收集
                json_block_encountered = True
                continue

            # 收集内容 - 但如果已经遇到JSON代码块，则不再收集任何内容
            if not json_block_encountered:
                if current_section == "overall":
                    sections["overall"] += line + "\n"
                elif current_section == "suggestions":
                    sections["suggestions"] += line + "\n"
                    self.logger.debug(f"第{i+1}行: 收集建议内容 - {line[:50]}...")
            else:
                # 已经遇到JSON代码块，跳过所有后续内容
                if current_section == "suggestions":
                    self.logger.debug(f"第{i+1}行: 跳过JSON代码块后的内容 - {line[:50]}...")

        # 如果没有找到明确的分段，尝试基于内容特征提取
        if not sections["overall"] or not sections["suggestions"]:
            sections = self._fallback_section_extraction(response_text)

        return sections

    def _fallback_section_extraction(self, response_text: str) -> Dict[str, str]:
        """备用的内容提取方法，基于内容特征而非标题匹配

        Args:
            response_text: AI模型返回的完整响应文本

        Returns:
            Dict[str, str]: 包含不同部分内容的字典
        """
        sections = {
            "overall": "",
            "suggestions": ""
        }

        # 尝试基于关键词和位置提取
        lines = response_text.strip().split('\n')

        # 查找总结与建议的开始位置
        suggestions_start = -1
        for i, line in enumerate(lines):
            if any(keyword in line for keyword in ["**总结**", "**建议**", "总结与建议"]):
                suggestions_start = i
                break

        if suggestions_start > 0:
            # 提取建议部分
            suggestions_lines = []
            for i in range(suggestions_start, len(lines)):
                line = lines[i].strip()
                if line and not line.startswith("```") and "五维度评分" not in line:
                    suggestions_lines.append(line)
                elif line.startswith("```") or "五维度评分" in line:
                    break
            sections["suggestions"] = "\n".join(suggestions_lines)

        # 提取整体评价部分（通常在开头）
        overall_lines = []
        for line in lines[:min(20, len(lines))]:  # 只看前20行
            line = line.strip()
            if line and not any(keyword in line for keyword in [
                "二、五维", "（一）", "（二）", "（三）", "（四）", "（五）"
            ]):
                overall_lines.append(line)
            else:
                break
        sections["overall"] = "\n".join(overall_lines)

        return sections

    def _extract_five_dimensions(self, response_text: str) -> Dict[str, str]:
        """从AI响应中提取五维度分析内容

        Args:
            response_text: AI模型返回的完整响应文本

        Returns:
            Dict[str, str]: 五维度分析内容字典
        """

        dimensions = {
            "student_learning": "",
            "teacher_teaching": "",
            "curriculum_nature": "",
            "classroom_culture": "",
            "social_emotion": ""
        }

        # 定义维度关键词映射
        dimension_keywords = {
            "student_learning": ["学生学习", "（一）学生学习", "1. 学生学习"],
            "teacher_teaching": ["教师教学", "（二）教师教学", "2. 教师教学"],
            "curriculum_nature": ["课程性质", "（三）课程性质", "3. 课程性质"],
            "classroom_culture": ["课堂文化", "（四）课堂文化", "4. 课堂文化"],
            "social_emotion": ["社会情感", "（五）社会情感", "5. 社会情感"]
        }

        lines = response_text.split('\n')
        current_dimension = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否遇到JSON代码块或其他结束标记，立即停止收集
            if line.startswith("```") or line.startswith("##") and any(keyword in line for keyword in ["三、", "四、", "总结", "建议", "评分"]):
                current_dimension = None
                break

            # 检查是否是维度标题
            for dim_key, keywords in dimension_keywords.items():
                if any(keyword in line for keyword in keywords):
                    current_dimension = dim_key
                    break

            # 如果当前在某个维度内，收集内容
            if current_dimension:
                # 跳过维度标题行
                if not any(any(keyword in line for keyword in keywords)
                          for keywords in dimension_keywords.values()):
                    # 检查是否到了下一个维度或其他部分
                    if any(keyword in line for keyword in ["四、", "##### （", "## ", "### "]):
                        if not any(any(keyword in line for keyword in keywords)
                                  for keywords in dimension_keywords.values()):
                            current_dimension = None
                            continue

                    # 再次检查是否是JSON代码块或结束符号
                    if line.startswith("```") or line == "}" or line.startswith("{"):
                        current_dimension = None
                        break

                    if current_dimension:
                        dimensions[current_dimension] += line + "\n"

        # 清理内容，移除可能的JSON片段
        for key in dimensions:
            content = dimensions[key].strip()

            # 移除可能的JSON代码块标记和内容
            lines = content.split('\n')
            cleaned_lines = []
            json_block_started = False

            for line in lines:
                line = line.strip()

                # 检测JSON代码块开始
                if line.startswith("```") or line == "{" or line.startswith("{"):
                    json_block_started = True
                    break

                # 检测单独的 } 符号（JSON结束）
                if line == "}" or line == "}```" or line.endswith("}"):
                    json_block_started = True
                    break

                # 检测其他结束标记
                if (line.startswith("##") and any(keyword in line for keyword in ["三、", "四、", "总结", "建议", "评分"])):
                    break

                if line and not json_block_started:
                    cleaned_lines.append(line)

            # 进一步清理：移除末尾可能的JSON片段
            final_content = '\n'.join(cleaned_lines).strip()

            # 检查并移除末尾的JSON符号
            while final_content.endswith('}') or final_content.endswith('```') or final_content.endswith('{'):
                if final_content.endswith('}'):
                    final_content = final_content[:-1].strip()
                elif final_content.endswith('```'):
                    final_content = final_content[:-3].strip()
                elif final_content.endswith('{'):
                    final_content = final_content[:-1].strip()
                else:
                    break

            dimensions[key] = final_content

            # 如果内容为空，提供默认内容
            if not dimensions[key]:
                dimension_names = {
                    "student_learning": "学生学习",
                    "teacher_teaching": "教师教学",
                    "curriculum_nature": "课程性质",
                    "classroom_culture": "课堂文化",
                    "social_emotion": "社会情感"
                }
                dimensions[key] = f"{dimension_names[key]}维度分析内容"

        return dimensions


    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态信息

        Returns:
            Dict[str, Any]: 服务状态信息
        """
        return {
            "service_name": "TeachingAnalyzer",
            "is_healthy": True,
            "ai_client_status": "active",
            "prompt_manager_status": "active",
            "chart_generator_status": "active"
        }

    def _save_markdown_report(
        self,
        analysis_dir: str,
        analysis_report: AnalysisReport,
        five_dimensions: FiveDimensionAnalysis,
        summary_suggestions: str,
        scores: dict
    ) -> str:
        """保存Markdown格式的分析报告

        参考 picturebook_generator_mcp 项目的文件保存格式，
        生成结构化的Markdown报告文件。

        Args:
            analysis_dir: 分析工作目录
            analysis_report: 分析报告对象
            five_dimensions: 五维度分析结果
            summary_suggestions: 总结与建议
            scores: 五维度评分数据

        Returns:
            str: Markdown报告文件路径
        """
        markdown_path = os.path.join(analysis_dir, "analysis_report.md")

        with open(markdown_path, 'w', encoding='utf-8') as f:
            # 标题和基本信息
            f.write("# 课堂教学五维度分析报告\n\n")
            f.write(f"**分析ID**: {analysis_report.analysis_id}\n\n")
            f.write(f"**分析时间**: {analysis_report.created_at.strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
            f.write(f"**分析耗时**: {analysis_report.analysis_duration:.2f}秒\n\n")
            f.write(f"**报告字数**: {analysis_report.word_count}字\n\n")
            f.write("---\n\n")

            # 一、总体评价
            f.write("## 一、总体评价\n\n")
            f.write(f"{analysis_report.overall_evaluation}\n\n")

            # 二、五维度分析
            f.write("## 二、五维度分析\n\n")

            f.write("### 1. 学生学习\n\n")
            f.write(f"{five_dimensions.student_learning}\n\n")

            f.write("### 2. 教师教学\n\n")
            f.write(f"{five_dimensions.teacher_teaching}\n\n")

            f.write("### 3. 课程性质\n\n")
            f.write(f"{five_dimensions.curriculum_nature}\n\n")

            f.write("### 4. 课堂文化\n\n")
            f.write(f"{five_dimensions.classroom_culture}\n\n")

            f.write("### 5. 社会情感\n\n")
            f.write(f"{five_dimensions.social_emotion}\n\n")

            # 三、总结与建议
            f.write("## 三、总结与建议\n\n")
            f.write(f"{summary_suggestions}\n\n")

            # 四、五维度评分
            f.write("## 四、五维度评分\n\n")
            f.write("| 评价维度 | 评分 | 等级 |\n")
            f.write("|---------|------|------|\n")

            def get_grade(score):
                if score >= 9.0:
                    return "优秀"
                elif score >= 7.0:
                    return "良好"
                elif score >= 5.0:
                    return "合格"
                elif score >= 3.0:
                    return "待改进"
                else:
                    return "需要重点改进"

            f.write(f"| 学生学习 | {scores.get('学生学习', 0):.1f} | {get_grade(scores.get('学生学习', 0))} |\n")
            f.write(f"| 教师教学 | {scores.get('教师教学', 0):.1f} | {get_grade(scores.get('教师教学', 0))} |\n")
            f.write(f"| 课程性质 | {scores.get('课程性质', 0):.1f} | {get_grade(scores.get('课程性质', 0))} |\n")
            f.write(f"| 课堂文化 | {scores.get('课堂文化', 0):.1f} | {get_grade(scores.get('课堂文化', 0))} |\n")
            f.write(f"| 社会情感 | {scores.get('社会情感', 0):.1f} | {get_grade(scores.get('社会情感', 0))} |\n")

            f.write("\n---\n\n")
            f.write("*本报告由教学评价AI系统自动生成*\n")

        self.logger.info(f"Markdown报告已保存: {markdown_path}")
        return markdown_path

    def _save_analysis_metadata(
        self,
        analysis_dir: str,
        transcript: str,
        analysis_report: AnalysisReport,
        usage_info: ModelUsageInfo
    ) -> str:
        """保存分析元数据

        参考 picturebook_generator_mcp 项目的元数据保存格式，
        保存完整的分析任务元数据信息。

        Args:
            analysis_dir: 分析工作目录
            transcript: 原始转录文本
            analysis_report: 分析报告对象
            usage_info: 模型用量信息

        Returns:
            str: 元数据文件路径
        """
        metadata = {
            "request_info": {
                "transcript_length": len(transcript)
            },
            "analysis_info": {
                "analysis_id": analysis_report.analysis_id,
                "timestamp": analysis_report.created_at.isoformat(),
                "analysis_duration": analysis_report.analysis_duration,
                "word_count": analysis_report.word_count
            },
            "results_summary": {
                "overall_evaluation_preview": analysis_report.overall_evaluation[:200] + "..." if len(analysis_report.overall_evaluation) > 200 else analysis_report.overall_evaluation,
                "five_dimension_scores": {
                    "student_learning": analysis_report.five_dimension_scores.student_learning,
                    "teacher_teaching": analysis_report.five_dimension_scores.teacher_teaching,
                    "curriculum_nature": analysis_report.five_dimension_scores.curriculum_nature,
                    "classroom_culture": analysis_report.five_dimension_scores.classroom_culture,
                    "social_emotion": analysis_report.five_dimension_scores.social_emotion
                },
                "average_score": (
                    analysis_report.five_dimension_scores.student_learning +
                    analysis_report.five_dimension_scores.teacher_teaching +
                    analysis_report.five_dimension_scores.curriculum_nature +
                    analysis_report.five_dimension_scores.classroom_culture +
                    analysis_report.five_dimension_scores.social_emotion
                ) / 5
            },
            "five_dimensions": {
                "student_learning": analysis_report.five_dimensions.student_learning,
                "teacher_teaching": analysis_report.five_dimensions.teacher_teaching,
                "curriculum_nature": analysis_report.five_dimensions.curriculum_nature,
                "classroom_culture": analysis_report.five_dimensions.classroom_culture,
                "social_emotion": analysis_report.five_dimensions.social_emotion
            },
            "summary_and_suggestions": analysis_report.summary_and_suggestions,
            "model_usage": {
                "vendor": usage_info.vendor,
                "model_name": usage_info.model_name,
                "input_tokens": usage_info.input_tokens,
                "output_tokens": usage_info.output_tokens,
                "total_tokens": usage_info.total_tokens,
                "response_time": usage_info.response_time
            },
            "files_generated": {
                "markdown_report": "analysis_report.md",
                "radar_chart_html": f"charts/radar_chart_{analysis_report.analysis_id}.html",
                "radar_chart_png": f"charts/radar_chart_{analysis_report.analysis_id}.png"
            }
        }

        metadata_path = os.path.join(analysis_dir, "metadata.json")
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

        self.logger.info(f"分析元数据已保存: {metadata_path}")
        return metadata_path
