"""AI客户端封装模块

提供统一的AI模型调用接口，支持多个AI服务提供商，包括智谱AI、SiliconFlow等。
"""

import time
from abc import ABC, abstractmethod
from typing import AsyncGenerator, Tu<PERSON>, Optional
from openai import AsyncOpenAI

from src.models import ModelUsageInfo, AIClientConfig
from src.logger import get_logger

logger = get_logger(__name__)


class BaseAIClient(ABC):
    """AI客户端基类"""
    
    def __init__(self, config: AIClientConfig):
        self.config = config
        self.client = None
        self._setup_client()
    
    @abstractmethod
    def _setup_client(self) -> None:
        """设置AI客户端"""
        pass
    
    @abstractmethod
    async def generate_text(self, prompt: str) -> Tu<PERSON>[str, ModelUsageInfo]:
        """生成文本并返回用量信息"""
        pass
    
    @abstractmethod
    async def stream_generate_text(self, prompt: str) -> AsyncGenerator[Tu<PERSON>[str, Optional[ModelUsageInfo]], None]:
        """流式生成文本"""
        pass
    
    def _estimate_tokens(self, text: str) -> int:
        """估算文本的Token数量（简单估算）"""
        # 中文：1个字符约等于1个token
        # 英文：1个单词约等于1.3个token
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        english_words = len(text.replace(' ', '').replace('\n', '')) - chinese_chars
        return chinese_chars + int(english_words * 1.3)
    
    def _calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """计算成本（需要根据具体提供商的定价策略实现）"""
        # 这里提供一个简单的估算，实际使用时需要根据具体提供商的定价
        # 智谱AI GLM-4 大约是 0.1元/1000tokens
        total_tokens = input_tokens + output_tokens
        return (total_tokens / 1000) * 0.1


class ZhipuAIClient(BaseAIClient):
    """智谱AI客户端"""
    
    def _setup_client(self) -> None:
        """设置智谱AI客户端"""
        try:
            self.client = AsyncOpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url or "https://open.bigmodel.cn/api/paas/v4/",
                timeout=self.config.timeout
            )
            logger.info(f"智谱AI客户端初始化成功，模型: {self.config.model_name}")
        except Exception as e:
            logger.error(f"智谱AI客户端初始化失败: {str(e)}")
            raise
    
    async def generate_text(self, prompt: str) -> Tuple[str, ModelUsageInfo]:
        """生成文本并返回用量信息"""
        start_time = time.time()
        
        try:
            response = await self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": prompt}],
                stream=False,
                temperature=0.7,
                max_tokens=2000
            )
            
            response_time = time.time() - start_time
            content = response.choices[0].message.content
            
            # 构建用量信息
            usage_info = ModelUsageInfo(
                provider=self.config.provider,
                model_name=self.config.model_name,
                input_tokens=response.usage.prompt_tokens,
                output_tokens=response.usage.completion_tokens,
                total_tokens=response.usage.total_tokens,
                cost_estimate=self._calculate_cost(
                    response.usage.prompt_tokens,
                    response.usage.completion_tokens
                ),
                response_time=response_time
            )
            
            logger.info(f"智谱AI调用成功: {usage_info.total_tokens} tokens, "
                       f"耗时 {response_time:.2f}s")
            
            return content, usage_info
            
        except Exception as e:
            logger.error(f"智谱AI调用失败: {str(e)}")
            raise
    
    async def stream_generate_text(self, prompt: str) -> AsyncGenerator[Tuple[str, Optional[ModelUsageInfo]], None]:
        """流式生成文本"""
        start_time = time.time()
        chunks = []
        
        try:
            stream = await self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": prompt}],
                stream=True,
                temperature=0.7,
                max_tokens=2000
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    chunks.append(content)
                    yield content, None  # 流式内容，暂无用量信息
            
            # 流式结束，计算总用量
            response_time = time.time() - start_time
            total_content = "".join(chunks)
            
            # 估算用量（实际项目中应从API响应获取）
            input_tokens = self._estimate_tokens(prompt)
            output_tokens = self._estimate_tokens(total_content)
            
            usage_info = ModelUsageInfo(
                provider=self.config.provider,
                model_name=self.config.model_name,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=input_tokens + output_tokens,
                cost_estimate=self._calculate_cost(input_tokens, output_tokens),
                response_time=response_time
            )
            
            logger.info(f"智谱AI流式调用完成: {usage_info.total_tokens} tokens, "
                       f"耗时 {response_time:.2f}s")
            
            yield "", usage_info  # 最后返回用量信息
            
        except Exception as e:
            logger.error(f"智谱AI流式调用失败: {str(e)}")
            raise


class SiliconFlowClient(BaseAIClient):
    """SiliconFlow客户端"""
    
    def _setup_client(self) -> None:
        """设置SiliconFlow客户端"""
        try:
            self.client = AsyncOpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url or "https://api.siliconflow.cn/v1",
                timeout=self.config.timeout
            )
            logger.info(f"SiliconFlow客户端初始化成功，模型: {self.config.model_name}")
        except Exception as e:
            logger.error(f"SiliconFlow客户端初始化失败: {str(e)}")
            raise
    
    async def generate_text(self, prompt: str) -> Tuple[str, ModelUsageInfo]:
        """生成文本并返回用量信息"""
        start_time = time.time()
        
        try:
            response = await self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": prompt}],
                stream=False,
                temperature=0.7,
                max_tokens=2000
            )
            
            response_time = time.time() - start_time
            content = response.choices[0].message.content
            
            # 构建用量信息
            usage_info = ModelUsageInfo(
                provider=self.config.provider,
                model_name=self.config.model_name,
                input_tokens=response.usage.prompt_tokens,
                output_tokens=response.usage.completion_tokens,
                total_tokens=response.usage.total_tokens,
                cost_estimate=self._calculate_cost(
                    response.usage.prompt_tokens,
                    response.usage.completion_tokens
                ),
                response_time=response_time
            )
            
            logger.info(f"SiliconFlow调用成功: {usage_info.total_tokens} tokens, "
                       f"耗时 {response_time:.2f}s")
            
            return content, usage_info
            
        except Exception as e:
            logger.error(f"SiliconFlow调用失败: {str(e)}")
            raise
    
    async def stream_generate_text(self, prompt: str) -> AsyncGenerator[Tuple[str, Optional[ModelUsageInfo]], None]:
        """流式生成文本"""
        start_time = time.time()
        chunks = []
        
        try:
            stream = await self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": prompt}],
                stream=True,
                temperature=0.7,
                max_tokens=2000
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    chunks.append(content)
                    yield content, None
            
            # 计算总用量
            response_time = time.time() - start_time
            total_content = "".join(chunks)
            
            input_tokens = self._estimate_tokens(prompt)
            output_tokens = self._estimate_tokens(total_content)
            
            usage_info = ModelUsageInfo(
                provider=self.config.provider,
                model_name=self.config.model_name,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=input_tokens + output_tokens,
                cost_estimate=self._calculate_cost(input_tokens, output_tokens),
                response_time=response_time
            )
            
            logger.info(f"SiliconFlow流式调用完成: {usage_info.total_tokens} tokens, "
                       f"耗时 {response_time:.2f}s")
            
            yield "", usage_info
            
        except Exception as e:
            logger.error(f"SiliconFlow流式调用失败: {str(e)}")
            raise


class DashScopeClient(BaseAIClient):
    """阿里云DashScope客户端（支持DeepSeek模型）"""

    def _setup_client(self) -> None:
        """设置DashScope客户端"""
        self.client = AsyncOpenAI(
            api_key=self.config.api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        logger.info(f"DashScope客户端初始化完成 - 模型: {self.config.model_name}")

    async def generate_text(self, prompt: str) -> Tuple[str, ModelUsageInfo]:
        """生成文本并返回用量信息"""
        try:
            start_time = time.time()

            response = await self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": prompt}],
                timeout=self.config.timeout
            )

            response_time = time.time() - start_time
            content = response.choices[0].message.content

            # 获取实际的token使用量
            usage = response.usage
            input_tokens = usage.prompt_tokens if usage else self._estimate_tokens(prompt)
            output_tokens = usage.completion_tokens if usage else self._estimate_tokens(content)
            total_tokens = usage.total_tokens if usage else (input_tokens + output_tokens)

            # 根据模型计算成本（参考文档中的价格）
            cost_estimate = self._calculate_cost(input_tokens, output_tokens)

            usage_info = ModelUsageInfo(
                provider="dashscope",
                model_name=self.config.model_name,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=total_tokens,
                cost_estimate=cost_estimate,
                response_time=response_time
            )

            return content, usage_info

        except Exception as e:
            logger.error(f"DashScope调用失败: {str(e)}")
            raise

    async def stream_generate_text(self, prompt: str) -> AsyncGenerator[Tuple[str, Optional[ModelUsageInfo]], None]:
        """流式生成文本"""
        try:
            start_time = time.time()

            stream = await self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": prompt}],
                stream=True,
                stream_options={"include_usage": True},
                timeout=self.config.timeout
            )

            content_chunks = []
            usage_info = None

            async for chunk in stream:
                if not chunk.choices:
                    # 最后一个chunk包含usage信息
                    if chunk.usage:
                        response_time = time.time() - start_time
                        usage = chunk.usage
                        cost_estimate = self._calculate_cost(usage.prompt_tokens, usage.completion_tokens)

                        usage_info = ModelUsageInfo(
                            provider="dashscope",
                            model_name=self.config.model_name,
                            input_tokens=usage.prompt_tokens,
                            output_tokens=usage.completion_tokens,
                            total_tokens=usage.total_tokens,
                            cost_estimate=cost_estimate,
                            response_time=response_time
                        )
                else:
                    delta = chunk.choices[0].delta
                    if delta.content:
                        content_chunks.append(delta.content)
                        yield delta.content, None

            # 返回最终的用量信息
            if usage_info:
                yield "", usage_info

        except Exception as e:
            logger.error(f"DashScope流式调用失败: {str(e)}")
            raise

    def _calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """计算成本（基于DashScope价格）"""
        # DeepSeek-V3价格：输入0.002元/千token，输出0.008元/千token
        if "deepseek-v3" in self.config.model_name.lower():
            input_cost = (input_tokens / 1000) * 0.002
            output_cost = (output_tokens / 1000) * 0.008
        # DeepSeek-R1价格：输入0.004元/千token，输出0.016元/千token
        elif "deepseek-r1" in self.config.model_name.lower():
            input_cost = (input_tokens / 1000) * 0.004
            output_cost = (output_tokens / 1000) * 0.016
        else:
            # 默认使用DeepSeek-V3价格
            input_cost = (input_tokens / 1000) * 0.002
            output_cost = (output_tokens / 1000) * 0.008

        return input_cost + output_cost


class AIClientFactory:
    """AI客户端工厂类"""

    _clients = {
        'zhipuai': ZhipuAIClient,
        'siliconflow': SiliconFlowClient,
        'dashscope': DashScopeClient,
    }
    
    @classmethod
    def create_client(cls, config: AIClientConfig) -> BaseAIClient:
        """
        创建AI客户端实例
        
        Args:
            config: AI客户端配置
            
        Returns:
            BaseAIClient: AI客户端实例
            
        Raises:
            ValueError: 不支持的AI提供商
        """
        if config.provider not in cls._clients:
            raise ValueError(f"不支持的AI提供商: {config.provider}")
        
        client_class = cls._clients[config.provider]
        return client_class(config)
    
    @classmethod
    def register_client(cls, provider: str, client_class: type) -> None:
        """
        注册新的AI客户端类
        
        Args:
            provider: 提供商名称
            client_class: 客户端类
        """
        cls._clients[provider] = client_class
        logger.info(f"注册AI客户端: {provider}")



