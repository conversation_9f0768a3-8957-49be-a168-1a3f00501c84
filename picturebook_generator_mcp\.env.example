# 豆包API配置（必需）
DOUBAO_API_KEY=your_doubao_api_key_here
DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
DOUBAO_IMAGE_MODEL=doubao-seedream-3-0-t2i-250415
DOUBAO_STORY_MODEL=doubao-1-5-pro-32k-250115

# 图片生成配置
IMAGE_SIZE=1280x720
GUIDANCE_SCALE=7.5
WATERMARK=false

# 输出配置
OUTPUT_DIR=outputs

# 日志配置
LOG_DIR=logs
LOG_LEVEL=INFO
LOG_CONSOLE=true

# 配置说明：
# DOUBAO_API_KEY: 豆包AI平台的API密钥，必须设置
# DOUBAO_BASE_URL: 豆包API的基础URL，一般使用默认值
# DOUBAO_IMAGE_MODEL: 图像生成模型名称
# DOUBAO_STORY_MODEL: 故事生成模型名称
# IMAGE_SIZE: 生成图片的尺寸，格式为"宽度x高度"
# GUIDANCE_SCALE: 引导比例，影响图片质量，通常7.5
# WATERMARK: 是否在生成的图片上添加水印
# OUTPUT_DIR: 绘本输出目录，相对于项目根目录
# LOG_DIR: 日志文件存储目录，相对于项目根目录
# LOG_LEVEL: 日志级别，可选：DEBUG, INFO, WARNING, ERROR, CRITICAL
# LOG_CONSOLE: 是否同时在控制台输出日志 