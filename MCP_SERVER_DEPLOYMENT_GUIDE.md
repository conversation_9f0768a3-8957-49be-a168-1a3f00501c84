## MCP Server 通用部署手册

本文档提供了一个通用的部署指南，旨在帮助开发者将遵循 **MCP 服务器开发规范** 构建的 MCP Server 部署到生产环境。请根据您的项目实际情况，替换文档中的占位符（例如 `<your_project_directory>`）。

### 一、环境准备

#### 1. 服务器配置

- **系统建议**：Ubuntu 22.04（推荐），2 核 4G 起步
- **安全组配置** 开放以下端口：
  - TCP 22（SSH）
  - TCP `<your_mcp_server_port>`（MCP 服务端口，例如 8000）
  - TCP 80 / 443（如配置 Nginx 和 HTTPS）

#### 2. 安装基础环境

- 通过SSH 连接到 ECS 服务器
  ```bash
  ssh root@<公网IP>
  ```

- 安装所需要的依赖
  ```bash
  # Redis 为可选项，仅当您的应用使用 Redis 作为缓存或消息队列时才需要安装
  sudo apt update && sudo apt install -y git redis-server
  sudo systemctl enable redis-server
  sudo systemctl start redis-server
  ```

- 安装 Python 环境与包管理工具：
  ```bash
  # 建议为您的项目创建一个专用目录
  mkdir /opt/mcp_servers
  cd /opt/mcp_servers
  
  # 安装 uv (高性能 Python 包安装器)
  curl -LsSf https://astral.sh/uv/install.sh | sh
  source $HOME/.cargo/env
  ```


### 二、项目部署

#### 1. 拉取项目代码

```bash
git clone <your_project_repository_url>
cd <your_project_directory>
```

#### 2. 创建并激活虚拟环境

```bash
# 在项目根目录下创建虚拟环境
uv venv
# 激活虚拟环境
source .venv/bin/activate
```

#### 3. 安装项目依赖

```bash
# 根据项目使用的依赖管理方式，安装依赖
# 方式一：如果使用 pyproject.toml (推荐)
uv pip install -e .

# 方式二：如果使用 requirements.txt
uv pip install -r requirements.txt
```


### 三、配置环境变量

根据开发规范，所有敏感凭证和环境相关配置都应通过环境变量进行管理。在项目根目录创建 `.env` 文件，并根据 `.env.example`（如果项目提供）或以下模板进行配置：

```ini
# ------------------- 关键凭证 -------------------
# 将所有 API Key 和敏感凭证放在此处
# 示例：
# VENDOR_API_KEY=sk-************
# DATABASE_URL=postgres://user:password@host:port/dbname

# ------------------- 大模型配置 -------------------
# 使用的大模型服务商、模型名称等
# LLM_PROVIDER_BASE_URL=https://api.example.com/v1
# LLM_MODEL_NAME=model-name
# LLM_TEMPERATURE=0.7

# ----------------- 服务与业务配置 -----------------
# 应用相关的配置项
# OUTPUT_DIR=outputs
# MAX_PARALLEL_WORKERS=10

# ------------------- 日志配置 -------------------
# 根据 logger.py 的实现进行配置
LOG_DIR=logs
LOG_LEVEL=INFO
# LOG_FILE=server.log # (可选)

# ------------------- 基础设施配置 -----------------
# Redis, Celery, 数据库等
REDIS_URL=redis://127.0.0.1:6379/0
# CELERY_BROKER_URL=redis://127.0.0.1:6379/1
# CELERY_RESULT_BACKEND=redis://127.0.0.1:6379/2
```


### 四、启动与测试服务

在部署到生产环境前，先在前台手动执行，确保服务能正常运行。

```bash
# 1. 启动主服务 (FastMCP)
# 激活虚拟环境后，在项目根目录执行
python main.py

# 2. (可选) 启动后台任务 Worker
# 如果您的项目使用了 Celery 等后台任务队列，请在另一个终端中启动 Worker。
# 将 <tasks_module> 替换为您的 Celery app 实例所在位置。
# celery -A <tasks_module>.celery_app worker --loglevel=info --concurrency=5
```


### 五、生产部署配置（systemd 自启动）

为了确保服务在服务器重启后能自动运行，推荐使用 `systemd` 进行托管。

#### 1. 配置 MCP 主服务

创建服务文件 `/etc/systemd/system/<project_name>.service` (例如 `ppt_generator.service`)：

```ini
[Unit]
Description=<Your Project Name> MCP Server
After=network.target

[Service]
# 推荐使用非 root 用户运行服务
User=<your_user>
Group=<your_group>
WorkingDirectory=/opt/mcp_servers/<your_project_directory>
ExecStart=/opt/mcp_servers/<your_project_directory>/.venv/bin/python main.py
Restart=always
RestartSec=10
Environment="PYTHONUNBUFFERED=1"

[Install]
WantedBy=multi-user.target
```

#### 2. (可选) 配置 Celery Worker 服务

如果需要，为 Celery Worker 创建服务文件 `/etc/systemd/system/celery-<project_name>.service`：

```ini
[Unit]
Description=Celery Worker for <Your Project Name>
After=network.target

[Service]
User=<your_user>
Group=<your_group>
WorkingDirectory=/opt/mcp_servers/<your_project_directory>
ExecStart=/opt/mcp_servers/<your_project_directory>/.venv/bin/celery -A <tasks_module>.celery_app worker --loglevel=info --concurrency=5
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 3. 启用 systemd 服务并设置开机自启

```bash
# 重新加载 systemd 配置
sudo systemctl daemon-reload

# 启用服务 (设置开机自启)
sudo systemctl enable <project_name>.service
# sudo systemctl enable celery-<project_name>.service # (如果配置了 Worker)

# 启动服务
sudo systemctl start <project_name>.service
# sudo systemctl start celery-<project_name>.service # (如果配置了 Worker)

# 查看服务状态
sudo systemctl status <project_name>.service
```

