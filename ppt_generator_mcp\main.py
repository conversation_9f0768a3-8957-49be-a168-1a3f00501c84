"""AI演示文稿生成MCP服务器主文件"""
import json
import sys

from fastmcp import FastMCP, Context

from src.ppt_generator.config import PPTGeneratorConfig
from src.ppt_generator.logger import get_logger, PPTLogger
from src.ppt_generator.models import PPTInfo
from src.ppt_generator.generators import PPTGeneratorService

# 初始化MCP服务器
mcp = FastMCP("PPT Generator MCP Server")

# 全局配置和服务实例
config = None
ppt_service = None
logger = get_logger(__name__)


def initialize_service():
    """初始化服务"""
    global config, ppt_service
    try:
        config = PPTGeneratorConfig.from_env()
        
        # 使用配置中的日志设置重新初始化日志系统
        PPTLogger.setup_logging(
            log_dir=config.log_dir,
            log_level=config.log_level,
            console_output=config.log_console
        )
        
        ppt_service = PPTGeneratorService(config)
        logger.info("PPT生成服务初始化成功")
    except Exception as e:
        logger.error(f"服务初始化失败: {str(e)}")
        raise

@mcp.tool()
async def generate_ppt(
    reference_content: str,
    ctx: Context,
    topic: str = "演示文稿"
) -> str:
    """
    生成HTML演示文稿
    
    Args:
        topic: 演示文稿主题（默认为"演示文稿"）
        reference_content: 参考资料内容（字符串格式，必填）
        ctx (Context): MCP上下文，由框架自动注入
    
    Returns:
        生成结果信息，包含生成的文件列表和索引页面路径
    """
    global ppt_service

    if not ppt_service:
        error_msg = "服务未初始化"
        logger.error(error_msg)
        return json.dumps({
            "success": False,
            "message": error_msg,
            "error": "PPT generation service not initialized"
        }, ensure_ascii=False)

    if not reference_content or not reference_content.strip():
        error_msg = "参考资料内容不能为空"
        logger.warning(f"PPT生成请求失败: {error_msg}")
        return json.dumps({
            "success": False,
            "message": error_msg,
            "error": "reference_content is required and cannot be empty"
        }, ensure_ascii=False)

    try:
        logger.info(f"开始生成PPT: 主题='{topic}', 内容长度={len(reference_content)}")
        
        # 创建PPT信息对象
        ppt_info = PPTInfo(
            topic=topic,
            reference_content=reference_content
        )

        # 生成PPT，并传入请求上下文
        result = await ppt_service.generate_ppt(ppt_info, ctx)
        
        if result.get("success", False):
            logger.info(f"PPT生成成功: {result.get('message', '')}")
        else:
            logger.error(f"PPT生成失败: {result.get('message', '')}")

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_msg = f"PPT生成过程中发生错误: {str(e)}"
        logger.exception(error_msg)  # 使用exception记录完整的错误堆栈
        return json.dumps({
            "success": False,
            "message": "PPT生成过程中发生错误",
            "error": str(e)
        }, ensure_ascii=False)

if __name__ == "__main__":
    # 初始化服务
    try:
        initialize_service()
        logger.info("启动PPT生成器MCP服务器...")
        # 运行MCP服务器（HTTP流模式）
        mcp.run(transport='streamable-http')
    except Exception as e:
        logger.critical(f"服务器启动失败: {str(e)}")
        sys.exit(1) 