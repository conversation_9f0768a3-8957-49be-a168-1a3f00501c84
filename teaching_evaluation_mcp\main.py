#!/usr/bin/env python3
"""
课堂教学五维分析MCP服务器
基于FastMCP框架的课堂教学分析服务
"""

import json
import sys
from typing import Optional
from fastmcp import FastMCP, Context
from src.teaching_evaluation.config import TeachingEvaluationConfig
from src.teaching_evaluation.logger import TeachingEvaluationLogger, get_logger
from src.teaching_evaluation.analyzer import TeachingAnalyzer

# 初始化MCP服务器
mcp = FastMCP("Teaching Evaluation MCP Server")

# 全局配置和服务实例
config: Optional[TeachingEvaluationConfig] = None
analyzer: Optional[TeachingAnalyzer] = None
logger = None  # 将在初始化后设置


def initialize_service():
    """初始化服务"""
    global config, analyzer, logger
    try:
        # 从环境变量加载配置
        config = TeachingEvaluationConfig.from_env()

        # 初始化日志系统
        TeachingEvaluationLogger.setup_logging(
            log_dir=config.log_dir,
            log_level=config.log_level,
            console_output=config.log_console
        )

        # 获取日志器
        logger = get_logger(__name__)

        # 初始化教学分析器
        analyzer = TeachingAnalyzer(config)

        logger.info("课堂教学分析服务初始化成功")
        logger.info(f"配置信息: 日志级别={config.log_level}, 输出目录={config.output_dir}")

    except Exception as e:
        if logger:
            logger.error(f"服务初始化失败: {str(e)}")
        else:
            print(f"服务初始化失败: {str(e)}")
        raise


@mcp.tool()
async def analyze_teaching_transcript(
    transcript: str,
    ctx: Context
) -> str:
    """分析课堂教学录音转录文本，生成五维度评价报告

    Args:
        transcript (str): 课堂教学录音转录文本.
        ctx (Context): MCP上下文，由框架自动注入.

    Returns:
        str: JSON格式的分析结果.
    """
    global analyzer

    if not analyzer:
        error_msg = "教学分析服务未初始化"
        logger.error(error_msg)
        if ctx:
            await ctx.error({
                "type": "service_not_initialized",
                "message": error_msg
            })
        return json.dumps({
            "success": False,
            "message": error_msg,
            "error": "Teaching analyzer not initialized"
        }, ensure_ascii=False)

    if not transcript or not transcript.strip():
        error_msg = "课堂转录文本不能为空"
        logger.warning(f"教学分析请求失败: {error_msg}")
        if ctx:
            await ctx.error({
                "type": "invalid_input",
                "message": error_msg
            })
        return json.dumps({
            "success": False,
            "message": error_msg,
            "error": "transcript is required and cannot be empty"
        }, ensure_ascii=False)

    try:
        # 添加调试日志
        logger.info(f"开始课堂教学分析: 内容长度={len(transcript)}字符")

        if ctx:
            await ctx.info({
                "status": "analysis_started",
                "message": "开始分析课堂教学转录文本",
                "transcript_length": len(transcript)
            })

        # 执行分析
        analysis_report, usage_info = await analyzer.analyze_teaching_transcript(
            transcript=transcript,
            ctx=ctx
        )

        # 构建成功响应
        # 构建返回结果，避免datetime序列化问题

        # 确保所有字段都是JSON可序列化的基本类型
        result = {
            "success": True,
            "message": "课堂教学分析完成",
            "analysis_id": str(analysis_report.analysis_id),
            "overall_evaluation": str(analysis_report.overall_evaluation),
            "five_dimensions": {
                "student_learning": str(analysis_report.five_dimensions.student_learning),
                "teacher_teaching": str(analysis_report.five_dimensions.teacher_teaching),
                "curriculum_nature": str(analysis_report.five_dimensions.curriculum_nature),
                "classroom_culture": str(analysis_report.five_dimensions.classroom_culture),
                "social_emotion": str(analysis_report.five_dimensions.social_emotion)
            },
            "five_dimension_scores": {
                "student_learning": float(analysis_report.five_dimension_scores.student_learning),
                "teacher_teaching": float(analysis_report.five_dimension_scores.teacher_teaching),
                "curriculum_nature": float(analysis_report.five_dimension_scores.curriculum_nature),
                "classroom_culture": float(analysis_report.five_dimension_scores.classroom_culture),
                "social_emotion": float(analysis_report.five_dimension_scores.social_emotion)
            },
            "summary_and_suggestions": str(analysis_report.summary_and_suggestions),
            "word_count": int(analysis_report.word_count),
            "analysis_duration": float(analysis_report.analysis_duration) if analysis_report.analysis_duration is not None else 0.0,
            "created_at": analysis_report.created_at.isoformat(),
            "usage_info": {
                "vendor": str(usage_info.vendor),
                "model_name": str(usage_info.model_name),
                "input_tokens": int(usage_info.input_tokens),
                "output_tokens": int(usage_info.output_tokens),
                "total_tokens": int(usage_info.total_tokens),
                "response_time": float(usage_info.response_time)
            },
            "files_generated": {
                "markdown_report": f"outputs/analysis_report.md",
                "metadata": f"outputs/metadata.json",
                "radar_chart_html": f"outputs/radar_chart_{analysis_report.analysis_id}.html",
                "radar_chart_png": f"outputs/radar_chart_{analysis_report.analysis_id}.png"
            }
        }

        logger.info(f"课堂教学分析成功: ID={analysis_report.analysis_id}")

        if ctx:
            await ctx.info({
                "status": "analysis_completed",
                "message": "分析完成",
                "analysis_id": analysis_report.analysis_id
            })

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_msg = f"课堂教学分析过程中发生错误: {str(e)}"
        logger.exception(error_msg)

        if ctx:
            # 尝试在发生错误时也通知客户端
            await ctx.error(error_msg)

        return json.dumps({
            "success": False,
            "message": "课堂教学分析过程中发生错误",
            "error": str(e)
        }, ensure_ascii=False)


if __name__ == "__main__":
    # 初始化服务
    try:
        initialize_service()
        logger.info("启动课堂教学分析MCP服务器...")
        logger.info(f"服务器配置: AI模型={config.ai_model_name}, 输出目录={config.output_dir}")

        # 运行MCP服务器（HTTP流模式）
        # 注意：确保客户端使用正确的URL: http://127.0.0.1:8000/mcp/
        mcp.run(transport='streamable-http')

    except KeyboardInterrupt:
        if logger:
            logger.info("收到中断信号，正在关闭服务器...")
        sys.exit(0)
    except Exception as e:
        if logger:
            logger.critical(f"服务器启动失败: {str(e)}")
        else:
            print(f"服务器启动失败: {str(e)}")
        sys.exit(1)
