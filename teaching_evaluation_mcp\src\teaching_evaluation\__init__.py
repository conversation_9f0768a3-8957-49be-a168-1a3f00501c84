"""
Teaching Evaluation MCP Server

一个基于 FastMCP 框架的课堂教学分析服务，提供：
- 课堂教学转录文本的智能分析
- 基于 DashScope DeepSeek-V3 模型的专业评估
- 五维度教学质量评价体系
- 实时进度反馈和详细日志记录
- 结构化分析报告和可视化雷达图

作者: Teaching Evaluation MCP Team
版本: 1.0.0
许可: MIT License
"""

__version__ = "1.0.0"
__author__ = "Teaching Evaluation MCP Team"
__email__ = "<EMAIL>"
__description__ = "Professional classroom teaching analysis MCP server"

# 导入已实现的组件
from .logger import (
    TeachingEvaluationLogger,
    get_logger
)

# 导入数据模型
from .models import (
    TeachingTranscript,
    TeachingAnalysisRequest,
    FiveDimensionAnalysis,
    ModelUsageInfo,
    ProgressUpdate,
    ErrorInfo,
    AnalysisReport,
    TeachingAnalysisResponse,
    AnalysisMetadata
)

# 导出主要组件
__all__ = [
    "__version__",
    "__author__",
    "__email__",
    "__description__",
    # 日志系统
    "TeachingEvaluationLogger",
    "get_logger",
    "setup_logging",
    "log_function_call",
    "log_performance",
    "log_model_usage",
    # 数据模型
    "TeachingTranscript",
    "TeachingAnalysisRequest",
    "FiveDimensionAnalysis",
    "ModelUsageInfo",
    "ProgressUpdate",
    "ErrorInfo",
    "AnalysisReport",
    "TeachingAnalysisResponse",
    "AnalysisMetadata",
    # 配置管理
    "TeachingEvaluationConfig",
    # 教学分析服务
    "TeachingAnalyzer",
    "TeachingAnalysisError",
    # 雷达图生成器
    "RadarChartGenerator"
]

# 导入配置管理
from .config import TeachingEvaluationConfig

# 导入教学分析服务
from .analyzer import TeachingAnalyzer, TeachingAnalysisError

# 导入雷达图生成器
from .chart_generator import RadarChartGenerator
