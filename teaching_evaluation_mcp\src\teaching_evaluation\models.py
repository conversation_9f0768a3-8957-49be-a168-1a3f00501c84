"""课堂教学分析数据模型

基于 Pydantic 的类型安全数据结构，提供：
- 输入输出验证
- 序列化/反序列化支持
- 完整的字段验证规则
- 扩展性设计

所有模型都遵循 MCP 开发规范，确保数据的一致性和可靠性。
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, model_validator
import uuid
import re


class TeachingTranscript(BaseModel):
    """课堂教学转录文本模型
    
    用于封装和验证课堂教学的转录文本内容，
    包含内容验证和元数据支持。
    """
    content: str = Field(description="转录文本内容")
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="转录文本的元数据信息（如录制时间、课程信息等）"
    )
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        """验证转录文本内容"""
        if not v or not v.strip():
            raise ValueError('转录文本内容不能为空')

        # 检查长度限制
        if len(v) > 50000:
            raise ValueError('转录文本长度不能超过50000字符')

        # 基本内容过滤（移除过多的空白字符）
        cleaned = re.sub(r'\s+', ' ', v.strip())
        return cleaned


class TeachingAnalysisRequest(BaseModel):
    """教学分析请求模型

    MCP工具的输入参数模型，包含分析所需的所有信息。
    """
    transcript: str = Field(description="课堂教学转录文本")
    analysis_id: Optional[str] = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="分析任务唯一标识符"
    )

    @field_validator('transcript')
    @classmethod
    def validate_transcript(cls, v):
        """验证转录文本"""
        if not v or not v.strip():
            raise ValueError('转录文本不能为空')

        if len(v) > 50000:
            raise ValueError('转录文本长度不能超过50000字符')

        return v.strip()


class FiveDimensionScores(BaseModel):
    """五维度评分模型

    存储五个维度的数值评分，用于雷达图生成。
    每个维度的评分范围为1.0-10.0分。
    """
    student_learning: float = Field(ge=1.0, le=10.0, description="学生学习维度评分")
    teacher_teaching: float = Field(ge=1.0, le=10.0, description="教师教学维度评分")
    curriculum_nature: float = Field(ge=1.0, le=10.0, description="课程性质维度评分")
    classroom_culture: float = Field(ge=1.0, le=10.0, description="课堂文化维度评分")
    social_emotion: float = Field(ge=1.0, le=10.0, description="社会情感维度评分")


class FiveDimensionAnalysis(BaseModel):
    """五维分析结果模型

    基于教育学理论的五个维度分析结果：
    1. 学生学习维度
    2. 教师教学维度
    3. 课程性质维度
    4. 课堂文化维度
    5. 社会情感维度

    每个维度存储详细的分析文本内容。
    """
    student_learning: str = Field(description="学生学习维度分析")
    teacher_teaching: str = Field(description="教师教学维度分析")
    curriculum_nature: str = Field(description="课程性质维度分析")
    classroom_culture: str = Field(description="课堂文化维度分析")
    social_emotion: str = Field(description="社会情感维度分析")

    @field_validator('student_learning', 'teacher_teaching', 'curriculum_nature',
                     'classroom_culture', 'social_emotion')
    @classmethod
    def validate_dimension_analysis(cls, v):
        """验证维度分析内容"""
        if not v or not v.strip():
            raise ValueError('维度分析内容不能为空')

        if len(v.strip()) < 10:
            raise ValueError('维度分析内容过短，至少需要10个字符')

        return v.strip()


class ModelUsageInfo(BaseModel):
    """模型用量信息模型

    记录AI模型调用的详细用量信息，用于成本控制和性能监控。
    注意：根据用户要求，移除了cost_yuan字段。
    """
    vendor: str = Field(description="提供模型的厂商，例如：dashscope, doubao")
    model_name: str = Field(description="本次调用的具体模型名称")
    input_tokens: int = Field(description="输入给模型的Token数量")
    output_tokens: int = Field(description="模型生成的Token数量")
    total_tokens: int = Field(description="总Token数量（输入+输出）")
    response_time: float = Field(description="API响应时间（秒）")
    
    @field_validator('vendor')
    @classmethod
    def validate_vendor(cls, v):
        """验证厂商名称"""
        if not v or not v.strip():
            raise ValueError('厂商名称不能为空')
        return v.strip().lower()

    @field_validator('model_name')
    @classmethod
    def validate_model_name(cls, v):
        """验证模型名称"""
        if not v or not v.strip():
            raise ValueError('模型名称不能为空')
        return v.strip()

    @field_validator('input_tokens', 'output_tokens', 'total_tokens')
    @classmethod
    def validate_tokens(cls, v):
        """验证Token数量"""
        if v < 0:
            raise ValueError('Token数量不能为负数')
        return v

    @field_validator('response_time')
    @classmethod
    def validate_response_time(cls, v):
        """验证响应时间"""
        if v < 0:
            raise ValueError('响应时间不能为负数')
        return v


class ProgressUpdate(BaseModel):
    """进度更新消息模型
    
    用于MCP实时通信的进度更新消息，
    支持状态、进度百分比和详细信息。
    """
    status: str = Field(description="当前状态")
    message: str = Field(description="状态描述")
    progress: Optional[float] = Field(
        default=None, 
        description="进度百分比 (0-100)"
    )
    details: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="详细信息"
    )
    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="消息时间戳"
    )
    
    @field_validator('status')
    @classmethod
    def validate_status(cls, v):
        """验证状态值"""
        if not v or not v.strip():
            raise ValueError('状态不能为空')

        # 定义允许的状态值
        allowed_statuses = {
            'started', 'analyzing', 'generating_report',
            'creating_chart', 'completed', 'error',
            'model_usage', 'streaming_text'
        }

        if v not in allowed_statuses:
            raise ValueError(f'无效的状态值: {v}')

        return v

    @field_validator('progress')
    @classmethod
    def validate_progress(cls, v):
        """验证进度百分比"""
        if v is not None:
            if not 0 <= v <= 100:
                raise ValueError('进度百分比必须在0-100之间')
        return v

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ErrorInfo(BaseModel):
    """错误信息模型
    
    用于统一的错误报告和处理。
    """
    error_type: str = Field(description="错误类型")
    error_message: str = Field(description="错误消息")
    error_details: Optional[str] = Field(
        default=None, 
        description="错误详情"
    )
    timestamp: datetime = Field(
        default_factory=datetime.now, 
        description="错误发生时间"
    )
    
    @field_validator('error_type')
    @classmethod
    def validate_error_type(cls, v):
        """验证错误类型"""
        if not v or not v.strip():
            raise ValueError('错误类型不能为空')

        # 定义允许的错误类型
        allowed_types = {
            'validation_error', 'api_error', 'timeout_error',
            'parsing_error', 'file_error', 'network_error',
            'authentication_error', 'rate_limit_error',
            'unexpected_error'
        }

        if v not in allowed_types:
            raise ValueError(f'无效的错误类型: {v}')

        return v

    @field_validator('error_message')
    @classmethod
    def validate_error_message(cls, v):
        """验证错误消息"""
        if not v or not v.strip():
            raise ValueError('错误消息不能为空')
        return v.strip()


class AnalysisReport(BaseModel):
    """分析报告模型

    完整的教学分析报告，包含所有分析结果和元数据。
    """
    analysis_id: str = Field(description="分析任务唯一标识符")
    overall_evaluation: str = Field(description="总体评价")
    five_dimensions: FiveDimensionAnalysis = Field(description="五维分析结果")
    five_dimension_scores: FiveDimensionScores = Field(description="五维度评分数据")
    summary_and_suggestions: str = Field(description="总结与建议")

    # 元数据
    created_at: datetime = Field(
        default_factory=datetime.now,
        description="创建时间"
    )
    word_count: int = Field(description="报告字数")
    analysis_duration: Optional[float] = Field(
        default=None,
        description="分析耗时（秒）"
    )

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    @field_validator('analysis_id')
    @classmethod
    def validate_analysis_id(cls, v):
        """验证分析ID"""
        if not v or not v.strip():
            raise ValueError('分析ID不能为空')
        return v.strip()

    @field_validator('overall_evaluation', 'summary_and_suggestions')
    @classmethod
    def validate_text_content(cls, v):
        """验证文本内容"""
        if not v or not v.strip():
            raise ValueError('文本内容不能为空')
        return v.strip()

    @field_validator('word_count')
    @classmethod
    def validate_word_count(cls, v):
        """验证字数"""
        if v < 0:
            raise ValueError('字数不能为负数')
        return v

    @field_validator('analysis_duration')
    @classmethod
    def validate_duration(cls, v):
        """验证分析耗时"""
        if v is not None and v < 0:
            raise ValueError('分析耗时不能为负数')
        return v


class TeachingAnalysisResponse(BaseModel):
    """教学分析响应模型
    
    MCP工具的输出结果模型，包含完整的分析结果。
    """
    success: bool = Field(description="分析是否成功")
    analysis_report: Optional[AnalysisReport] = Field(
        default=None,
        description="分析报告（成功时提供）"
    )
    error_info: Optional[ErrorInfo] = Field(
        default=None,
        description="错误信息（失败时提供）"
    )
    model_usage: Optional[ModelUsageInfo] = Field(
        default=None,
        description="模型用量信息"
    )
    
    @model_validator(mode='after')
    def validate_response(self):
        """验证响应的一致性"""
        if self.success:
            if not self.analysis_report:
                raise ValueError('成功响应必须包含分析报告')
            if self.error_info:
                raise ValueError('成功响应不应包含错误信息')
        else:
            if not self.error_info:
                raise ValueError('失败响应必须包含错误信息')
            if self.analysis_report:
                raise ValueError('失败响应不应包含分析报告')

        return self


class AnalysisMetadata(BaseModel):
    """分析元数据模型

    用于记录分析过程的详细元数据信息。
    """
    analysis_id: str = Field(description="分析任务ID")
    transcript_length: int = Field(description="转录文本长度")
    report_word_count: int = Field(description="报告字数")
    analysis_duration: float = Field(description="分析耗时")
    model_usage: ModelUsageInfo = Field(description="模型用量信息")
    created_at: datetime = Field(description="创建时间")
    config_snapshot: Dict[str, Any] = Field(description="配置快照")
    
    @field_validator('transcript_length', 'report_word_count')
    @classmethod
    def validate_counts(cls, v):
        """验证计数字段"""
        if v < 0:
            raise ValueError('计数字段不能为负数')
        return v

    @field_validator('analysis_duration')
    @classmethod
    def validate_duration(cls, v):
        """验证分析耗时"""
        if v < 0:
            raise ValueError('分析耗时不能为负数')
        return v

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
