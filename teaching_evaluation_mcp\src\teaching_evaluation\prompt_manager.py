"""教学分析提示词管理模块

基于设计文档实现的简化版提示词管理器，支持：
- 统一的提示词模板
- 五维度评分数据提取
- 自定义要求集成
"""

import json
import re
from typing import Dict, Optional
from pathlib import Path

from .logger import get_logger


class PromptManager:
    """提示词管理器（基于prompt_fixed.md设计，支持评分功能）"""

    def __init__(self, templates_dir: str = "templates"):
        """初始化提示词管理器
        
        Args:
            templates_dir: 模板文件目录路径
        """
        self.logger = get_logger(__name__)
        self.templates_dir = Path(templates_dir)
        self.base_template = self._load_base_template()
        self.logger.info("提示词管理器初始化完成")

    def build_analysis_prompt(self, transcript: str) -> str:
        """构建统一的教学分析提示词（包含评分要求）

        Args:
            transcript: 课堂教学转录文本

        Returns:
            str: 完整的分析提示词
        """
        # 预处理转录文本：去除时间戳和发言人标识
        processed_transcript = self._preprocess_transcript(transcript)

        # 构建完整提示词
        prompt = self.base_template.format(
            transcript=processed_transcript,
            custom_requirements=""  # 移除自定义要求功能
        )

        self.logger.info(f"构建分析提示词完成 - 原始长度: {len(transcript)}, 处理后长度: {len(processed_transcript)}")
        return prompt

    def _preprocess_transcript(self, transcript: str) -> str:
        """预处理转录文本，去除时间戳和发言人标识

        Args:
            transcript: 原始转录文本

        Returns:
            str: 处理后的纯对话内容
        """
        import re

        lines = transcript.split('\n')
        processed_lines = []

        for line in lines:
            line = line.strip()

            # 跳过空行
            if not line:
                continue

            # 跳过文档头信息（包含日期、作者等）
            if self._is_document_header(line):
                continue

            # 跳过时间戳行（如：发言人   00:00）
            if self._is_timestamp_line(line):
                continue

            # 处理包含发言人标识的对话行
            cleaned_line = self._remove_speaker_labels(line)
            if cleaned_line:
                processed_lines.append(cleaned_line)

        # 合并处理后的内容
        processed_text = '\n'.join(processed_lines)

        # 清理多余的空行
        processed_text = re.sub(r'\n\s*\n', '\n\n', processed_text)
        processed_text = processed_text.strip()

        self.logger.info(f"文本预处理完成 - 原始行数: {len(lines)}, 处理后行数: {len(processed_lines)}")
        return processed_text

    def _is_document_header(self, line: str) -> bool:
        """判断是否为文档头信息"""
        import re

        # 匹配日期格式
        if re.match(r'\d{4}年\d{1,2}月\d{1,2}日', line):
            return True

        # 匹配作者信息（包含 - 的行）
        if ' - ' in line and not '：' in line and not ':' in line:
            return True

        return False

    def _is_timestamp_line(self, line: str) -> bool:
        """判断是否为时间戳行"""
        import re

        # 匹配 "发言人   HH:MM" 格式
        if re.match(r'^发言人\s+\d{1,2}:\d{2}$', line):
            return True

        return False

    def _remove_speaker_labels(self, line: str) -> str:
        """移除发言人标识，保留对话内容"""
        import re

        # 移除行首的发言人标识（如：老师：、学生：、教师：等）
        patterns = [
            r'^[^：:]*[：:]\s*',  # 匹配 "姓名：" 或 "姓名:" 格式
            r'^(老师|教师|学生|同学)\s*[：:]\s*',  # 匹配常见角色标识
            r'^(Teacher|Student)\s*[：:]\s*',  # 匹配英文角色标识
            r'^\w+\s*[：:]\s*',  # 匹配单词+冒号格式
        ]

        cleaned_line = line
        for pattern in patterns:
            cleaned_line = re.sub(pattern, '', cleaned_line)

        return cleaned_line.strip()

    def extract_scores_from_response(self, response: str) -> Dict[str, float]:
        """从AI响应中提取五维度评分数据
        
        Args:
            response: AI分析响应文本
            
        Returns:
            Dict[str, float]: 五维度评分字典
        """
        # 查找JSON格式的评分数据
        json_pattern = r'```json\s*(\{[^}]*\})\s*```'
        match = re.search(json_pattern, response, re.DOTALL)

        if match:
            try:
                scores_data = json.loads(match.group(1))
                self.logger.info("成功从JSON格式中提取评分数据")
                return scores_data
            except json.JSONDecodeError as e:
                self.logger.warning(f"JSON解析失败: {e}，使用默认评分")

        # 如果JSON解析失败，尝试备用方法
        return self._extract_scores_fallback(response)

    def _extract_scores_fallback(self, response: str) -> Dict[str, float]:
        """备用的评分提取方法
        
        Args:
            response: AI响应文本
            
        Returns:
            Dict[str, float]: 五维度评分字典
        """
        scores = {}
        dimensions = ["学生学习", "教师教学", "课程性质", "课堂文化", "社会情感"]

        # 使用正则表达式提取评分
        for dimension in dimensions:
            found = False
            # 尝试多种格式
            patterns = [
                rf"【{dimension}：(\d+(?:\.\d+)?)分】",
                rf"{dimension}[：:]\s*(\d+(?:\.\d+)?)分",
                rf"{dimension}.*?(\d+(?:\.\d+)?)分"
            ]

            for pattern in patterns:
                match = re.search(pattern, response)
                if match:
                    try:
                        score = float(match.group(1))
                        scores[dimension] = score
                        found = True
                        break
                    except ValueError:
                        continue

            if not found:
                self.logger.warning(f"未能从分析文本中提取{dimension}的评分，使用默认值")
                scores[dimension] = 7.0  # 使用中等偏上的默认分数

        self.logger.info(f"评分提取完成 - 成功提取: {len([k for k, v in scores.items() if v != 7.0])}/5 个维度")
        return scores

    def _load_base_template(self) -> str:
        """加载基础提示词模板
        
        Returns:
            str: 基础提示词模板
        """
        template_path = self.templates_dir / "base_template.txt"
        if template_path.exists():
            self.logger.info(f"从文件加载提示词模板: {template_path}")
            return template_path.read_text(encoding='utf-8')

        # 基于prompt_fixed.md的完整模板（增加评分功能）
        self.logger.info("使用内置提示词模板")
        return """# 课堂教学分析专家提示词

## 角色定义
你是一位顶级的教育评估专家和教学分析顾问，拥有超过20年的课堂观察和教师发展指导经验。你的专业知识覆盖了从人文社科到自然科学的多个小学学科领域。你尤其擅长从课堂的细微互动中，洞察其背后深层的教育学和心理学逻辑，并能结合不同学科的特质进行精准分析。你的分析报告以其深刻的洞察力、严谨的逻辑、丰富的细节和专业的术语而著称。

## 核心任务
你的任务是根据下面提供的【课堂教学转录文本】，撰写一份全面、深入、专业的《课堂教学分析报告》，并提供五维度评分数据。

**重要约束：**
- 必须严格按照指定的五个维度进行评分：学生学习、教师教学、课程性质、课堂文化、社会情感
- 不得添加、删除或修改任何维度名称
- 评分必须以标准JSON格式输出，确保程序可解析

## 评价框架与要求
报告必须严格遵循以下五维评价框架，覆盖全部25个评价视角。报告总字数必须超过2000字。

### 五维评分要求
在完成定性分析的基础上，必须为以下五个维度提供量化评分（1-10分制）：
1. **学生学习**：综合评估学生在准备、倾听、互动、自主、达成五个方面的表现
2. **教师教学**：综合评估教师在环节、呈示、对话、指导、机智五个方面的表现
3. **课程性质**：综合评估课程在目标、内容、实施、评价、资源五个方面的表现
4. **课堂文化**：综合评估课堂在思考、民主、创新、关爱、特质五个方面的表现
5. **社会情感**：综合评估学生在情绪力、共情力、合作力、责任力、创造力五个方面的表现

评分标准：
- 9-10分：优秀，该维度表现突出，值得推广
- 7-8分：良好，该维度表现较好，有亮点
- 5-6分：合格，该维度表现一般，符合基本要求
- 3-4分：待改进，该维度存在明显不足
- 1-2分：需要重点改进，该维度表现较差

### 报告结构

#### 1. 一、总体评价（约200-300字）
对整堂课进行一个高度概括、观点鲜明的总体评价，点出其核心亮点与主要待改进之处。

#### 2. 二、五维评估
这是报告的主体，必须对以下五个维度及其所有子项进行逐一分析。

**对于每一个子项（共25个），你的分析都必须遵循以下模式：**

1. **观点陈述**：首先明确给出你对该子项的评价
2. **证据列举**：然后，【必须】从【课堂教学转录文本】中引用1到2个具体的课堂情景、师生对话或行为作为核心证据
3. **深度分析**：最后，对该证据进行深入的教育学分析，阐述这个行为反映了什么、带来了什么影响、有何教育价值或潜在问题

每个子项的分析论述部分（观点+证据+分析）应不少于80字。

##### 五维框架详情：

**（一）学生学习**
- 准备：学生课前知识、物品或心理准备情况
- 倾听：学生是否能有效倾听教师指导和同伴发言
- 互动：学生参与课堂问答、讨论和活动的频率与质量
- 自主：学生在没有教师直接指令下，自主探索、思考和操作的表现
- 达成：学生对本节课核心知识与技能目标的掌握程度

**（二）教师教学**
- 环节：教学流程的设计是否清晰、完整、逻辑连贯
- 呈示：教师呈现教学内容的方式是否清晰、有效、富有吸引力
- 对话：师生、生生对话的质量，教师是否能有效引导和启发
- 指导：教师在学生活动中的巡视、指导和支持是否及时、有效
- 机智：教师处理课堂意外情况或学生独特想法的应变能力

**（三）课程性质**
- 目标：本课的教学目标是否明确、恰当，并得到有效落实
- 内容：教学内容的选择是否贴近学生生活，具有科学性、趣味性和探究空间
- 实施：教学计划的实际执行情况，节奏和时间把控是否合理
- 评价：教师在课堂中运用的评价方式是否多样、有效，能否激励学生
- 资源：对教具、文本、多媒体等教学资源的利用是否充分、得当

**（四）课堂文化**
- 思考：课堂是否鼓励并引发了学生的深度思考和批判性思维
- 民主：课堂氛围是否民主、平等，学生能否自由安全地表达不同意见
- 创新：课堂是否鼓励学生提出创新的想法或解决方法
- 关爱：师生关系是否融洽，教师是否关注到学生的情感需求
- 特质：课堂是否鲜明地体现了本堂课所属学科的核心特质

**（五）社会情感**
- 情绪力：学生在课堂上整体的情绪状态是积极投入还是消极被动
- 共情力：学生是否表现出理解、尊重和体谅他人的能力
- 合作力：学生在同桌或小组活动中展现出的合作意愿和能力
- 责任力：学生对自己学习任务的负责态度
- 创造力：学生在解决问题过程中展现出的想象力和创造性思维

#### 3. 三、总结与建议（约200-300字）
在五维分析的基础上，对整堂课进行最终总结，并针对待改进之处，提出2-3条具体、可操作的教学建议。

#### 4. 四、五维度评分
**重要：必须严格按照以下JSON格式输出评分，不得添加、删除或修改维度名称**

```json
{{
  "学生学习": 8.5,
  "教师教学": 7.8,
  "课程性质": 8.2,
  "课堂文化": 7.5,
  "社会情感": 8.0
}}
```

**评分要求：**
- 必须且仅能包含上述五个维度，维度名称必须完全一致
- 每个维度的评分必须是1.0-10.0之间的数值（可包含小数）
- 不得添加任何其他维度或字段
- JSON格式必须严格正确，可被程序解析

## 工作流程

### 1. 识别与定位
首先，快速通读下方【课堂教学转录文本】，判断并识别出这堂课的【所属学科】。

### 2. 通读并吸收
仔细、完整地阅读文本，在理解课堂完整流程和所有细节的同时，特别留意那些能反映该学科学科特质的环节。

### 3. 提炼与归类
将文本中的关键事件和师生互动，与上述25个评价视角进行匹配。

### 4. 逐项分析与撰写
严格按照【评价框架与要求】中的结构和细则，开始撰写报告。

### 5. 审查与定稿
完成初稿后，通读全文，检查是否满足所有要求，并润色语言，使其更流畅、专业。

## 注意事项
- 分析必须客观、公正，既要充分肯定优点，也要中肯地指出问题
- 避免使用空洞、模板化的套话，所有评价都必须与提供的文本内容紧密结合
- 语言风格：专业、严谨、流畅，同时带有关怀的温度

---

【课堂教学转录文本】
{transcript}

{custom_requirements}"""
